spring:
  application:
    name: medreserve-backend

  datasource:
    url: ${DB_URL:**************************************************************************************************************
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  jackson:
    serialization:
      indent_output: true

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

server:
  port: ${PORT:8080}
  error:
    include-message: always

# External Services Configuration
ml:
  service:
    url: ${ML_SERVICE_URL:https://medreserve-ml.onrender.com}

chatbot:
  service:
    url: ${CHATBOT_SERVICE_URL:https://medreserve-chatbot.onrender.com}

disease:
  prediction:
    service:
      url: ${DISEASE_PREDICTION_SERVICE_URL:http://localhost:8003}
    fallback:
      enabled: ${DISEASE_PREDICTION_FALLBACK_ENABLED:true}

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:5173}

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:myLocalSecretKey123456789012345678901234567890}
  expiration: ${JWT_EXPIRATION:86400000}
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000}

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:5173}

logging:
  level:
    root: INFO
    org.springframework.web: DEBUG
    org.hibernate: INFO
    com.medreserve: DEBUG

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
