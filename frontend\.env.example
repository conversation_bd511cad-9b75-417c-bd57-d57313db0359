# Frontend Environment Variables
# Copy this file to .env.local and update the values

# API Configuration
VITE_API_BASE_URL=http://localhost:8080/api

# Production API URL (for deployment)
# VITE_API_BASE_URL=https://medreserve-ai-backend.onrender.com/api

# App Configuration
VITE_APP_NAME=MedReserve AI
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_CHAT=true
VITE_ENABLE_AI_FEATURES=true

# Development Settings
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=info
