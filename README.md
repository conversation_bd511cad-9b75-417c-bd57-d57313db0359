# 🏥 MedReserve AI - Healthcare Management Platform

A comprehensive healthcare management platform with AI-driven solutions, featuring a Spring Boot backend and React frontend.

## 🌟 Features

### 🔐 Authentication & Authorization
- Role-based access control (<PERSON><PERSON>, Doctor, Admin, Master Admin)
- JWT-based authentication with refresh tokens
- Secure password hashing and validation

### 👥 User Management
- Patient registration and profile management
- Doctor profiles with specializations and availability
- Admin dashboard for user management
- Real-time user statistics and analytics

### 📅 Appointment System
- Online appointment booking with doctor availability
- Appointment management and scheduling
- Real-time notifications and reminders
- Calendar integration and time slot management

### 🏥 Medical Records
- Electronic health records (EHR) management
- Medical report upload and storage
- Prescription management and tracking
- Patient medical history and documentation

### 🤖 AI-Powered Features
- **Symptom Checker**: ML-powered symptom analysis and specialty recommendation
- **Healthcare Chatbot**: AI assistant for medical queries and support
- **Predictive Analytics**: Health risk assessment and wellness scoring
- **Smart Recommendations**: Personalized health tips and advice

### 📊 Analytics & Reporting
- Real-time dashboard with healthcare metrics
- Patient and doctor analytics
- Appointment statistics and trends
- System health monitoring

### 🔒 Security & Compliance
- HIPAA-compliant data handling
- Encrypted data transmission and storage
- Audit logging and access tracking
- Rate limiting and DDoS protection

## 🏗️ Architecture

### Backend (Spring Boot)
- **Framework**: Spring Boot 3.2.0 with Java 17
- **Database**: MySQL with H2 for development
- **Security**: Spring Security with JWT
- **API Documentation**: OpenAPI 3.0 (Swagger)
- **Real-time**: WebSocket support for chat and notifications

### Frontend (React + Vite)
- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Library**: Material-UI for consistent design
- **State Management**: React Query for server state
- **Routing**: React Router for client-side navigation

### AI Services
- **ML Service**: Python FastAPI for symptom analysis
- **Chatbot Service**: Python FastAPI for conversational AI
- **Model Training**: Scikit-learn for medical specialty prediction

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Node.js 18+
- MySQL 8.0+ (optional, H2 included for development)
- Maven 3.6+

### Backend Setup
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### AI Services Setup
```bash
# ML Service
cd backend/ml
pip install -r requirements.txt
python main.py

# Chatbot Service
cd backend/chatbot
pip install -r requirements.txt
python main.py
```

## 🌐 Deployment

### Backend (Render)
1. Connect your GitHub repository to Render
2. Use the provided `render.yaml` configuration
3. Set environment variables in Render dashboard
4. Deploy with automatic builds on push

### Frontend (GitHub Pages)
1. Update `homepage` in `package.json` with your GitHub Pages URL
2. Run deployment:
```bash
cd frontend
npm run deploy
```

## 🔧 Configuration

### Environment Variables

#### Backend
```env
# Database
DB_URL=*****************************************
DB_USERNAME=root
DB_PASSWORD=password

# Server
SERVER_PORT=8080

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRATION=86400000

# Email
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# AI Services
ML_SERVICE_URL=http://localhost:8001
CHATBOT_SERVICE_URL=http://localhost:5005
```

#### Frontend
```env
VITE_API_BASE_URL=http://localhost:8080/api
VITE_ML_SERVICE_URL=http://localhost:8001
VITE_CHATBOT_SERVICE_URL=http://localhost:5005
VITE_APP_NAME=MedReserve AI
VITE_APP_VERSION=1.0.0
```

## 📚 API Documentation

Once the backend is running, visit:
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API Docs**: http://localhost:8080/api/api-docs

## 🔐 Demo Credentials

- **Patient**: `<EMAIL>` / `password123`
- **Doctor**: `<EMAIL>` / `password123`
- **Admin**: `<EMAIL>` / `password123`
- **Master Admin**: `<EMAIL>` / `password123`

## 🧪 Testing

### Backend Tests
```bash
cd backend
mvn test
```

### Frontend Tests
```bash
cd frontend
npm test
```

### API Testing
```bash
cd backend/scripts
./test-api.sh
```

## 📁 Project Structure

```
MedReserve/
├── backend/                    # Spring Boot backend
│   ├── src/main/java/         # Java source code
│   ├── src/main/resources/    # Configuration files
│   ├── ml/                    # ML service (Python)
│   ├── chatbot/              # Chatbot service (Python)
│   ├── scripts/              # Deployment and test scripts
│   └── pom.xml               # Maven configuration
├── frontend/                  # React frontend
│   ├── src/                  # React source code
│   ├── public/               # Static assets
│   ├── package.json          # NPM configuration
│   └── vite.config.ts        # Vite configuration
└── README.md                 # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or create an issue on GitHub.

## 🙏 Acknowledgments

- Spring Boot team for the excellent framework
- React team for the powerful UI library
- Material-UI for the beautiful components
- All contributors and testers
