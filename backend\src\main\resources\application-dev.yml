spring:
  application:
    name: medreserve-backend

  # H2 Database for local development
  datasource:
    url: jdbc:h2:mem:medreserve
    username: sa
    password: 
    driver-class-name: org.h2.Driver

  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    hibernate:
      ddl-auto: create-drop  # Recreate schema on startup for dev
    show-sql: true
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  jackson:
    serialization:
      indent_output: true

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# JWT Configuration
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000  # 24 hours

# CORS Configuration
cors:
  allowed-origins: http://localhost:3000,http://localhost:3001,http://localhost:5173

# External Services
external:
  ml-service:
    url: http://localhost:8001
  chatbot-service:
    url: http://localhost:8002

# Chatbot Configuration
chatbot:
  supported-languages:
    - en
    - hi
    - te

# Logging
logging:
  level:
    com.medreserve: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
