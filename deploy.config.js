/**
 * MedReserve AI - Universal Deployment Configuration
 * Works with Vercel, Netlify, Render, Railway, Heroku, and any Docker platform
 * 
 * Usage:
 *   - Copy relevant sections to your deployment platform
 *   - All configurations are platform-agnostic
 *   - Environment variables work universally
 */

module.exports = {
  // ===========================================
  // FRONTEND DEPLOYMENT (React + Vite)
  // ===========================================
  frontend: {
    // Build configuration
    build: {
      command: "npm run build",
      directory: "frontend",
      outputDir: "dist",
      nodeVersion: "18"
    },
    
    // Environment variables (set in your deployment platform)
    env: {
      VITE_API_URL: process.env.VITE_API_URL || "https://your-backend-url.com",
      VITE_APP_NAME: "MedReserve AI",
      VITE_APP_VERSION: "1.0.0"
    },
    
    // Vercel configuration (vercel.json)
    vercel: {
      version: 2,
      builds: [
        {
          src: "frontend/package.json",
          use: "@vercel/node"
        }
      ],
      routes: [
        {
          src: "/(.*)",
          dest: "/frontend/dist/index.html"
        }
      ],
      env: {
        VITE_API_URL: process.env.VITE_API_URL
      }
    },
    
    // Netlify configuration (_redirects and netlify.toml)
    netlify: {
      redirects: "/*    /index.html   200",
      toml: {
        build: {
          base: "frontend/",
          command: "npm run build",
          publish: "dist/"
        },
        context: {
          production: {
            environment: {
              VITE_API_URL: process.env.VITE_API_URL
            }
          }
        }
      }
    }
  },

  // ===========================================
  // BACKEND DEPLOYMENT (Spring Boot)
  // ===========================================
  backend: {
    // Build configuration
    build: {
      command: "mvn clean package -DskipTests",
      directory: "backend",
      outputFile: "target/*.jar",
      javaVersion: "17"
    },
    
    // Environment variables (set in your deployment platform)
    env: {
      // Database
      DB_URL: process.env.DB_URL || "*******************************************",
      DB_USERNAME: process.env.DB_USERNAME || "postgres",
      DB_PASSWORD: process.env.DB_PASSWORD || "password",
      
      // JWT
      JWT_SECRET: process.env.JWT_SECRET || "your-secret-key-here",
      JWT_EXPIRATION: process.env.JWT_EXPIRATION || "86400000",
      
      // CORS
      CORS_ALLOWED_ORIGINS: process.env.CORS_ALLOWED_ORIGINS || "https://your-frontend-url.com",
      
      // External Services
      ML_SERVICE_URL: process.env.ML_SERVICE_URL || "https://your-ml-service.com",
      CHATBOT_SERVICE_URL: process.env.CHATBOT_SERVICE_URL || "https://your-chatbot-service.com",
      
      // Mail (optional)
      MAIL_HOST: process.env.MAIL_HOST || "smtp.gmail.com",
      MAIL_PORT: process.env.MAIL_PORT || "587",
      MAIL_USERNAME: process.env.MAIL_USERNAME || "",
      MAIL_PASSWORD: process.env.MAIL_PASSWORD || "",
      
      // Server
      PORT: process.env.PORT || "8080",
      SPRING_PROFILES_ACTIVE: process.env.SPRING_PROFILES_ACTIVE || "prod"
    },
    
    // Render configuration (render.yaml)
    render: {
      services: [
        {
          type: "web",
          name: "medreserve-backend",
          env: "java",
          buildCommand: "mvn clean package -DskipTests",
          startCommand: "java -jar target/medreserve-backend-0.0.1-SNAPSHOT.jar",
          envVars: [
            { key: "SPRING_PROFILES_ACTIVE", value: "prod" },
            { key: "DB_URL", fromDatabase: { name: "medreserve-db", property: "connectionString" } },
            { key: "JWT_SECRET", generateValue: true },
            { key: "CORS_ALLOWED_ORIGINS", value: "https://your-frontend-url.com" }
          ]
        }
      ],
      databases: [
        {
          name: "medreserve-db",
          databaseName: "medreserve",
          user: "medreserve_user"
        }
      ]
    },
    
    // Railway configuration (railway.toml)
    railway: {
      build: {
        builder: "NIXPACKS"
      },
      deploy: {
        startCommand: "java -jar target/medreserve-backend-0.0.1-SNAPSHOT.jar",
        healthcheckPath: "/actuator/health",
        healthcheckTimeout: 300
      }
    },
    
    // Heroku configuration (Procfile)
    heroku: {
      procfile: "web: java -Dserver.port=$PORT -jar target/medreserve-backend-0.0.1-SNAPSHOT.jar"
    }
  },

  // ===========================================
  // DOCKER DEPLOYMENT (Universal)
  // ===========================================
  docker: {
    // Frontend Dockerfile
    frontend: `
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]`,

    // Backend Dockerfile
    backend: `
FROM openjdk:17-jdk-slim
WORKDIR /app
COPY target/*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]`,

    // Docker Compose
    compose: `
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:8080
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_URL=************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - JWT_SECRET=your-secret-key
      - CORS_ALLOWED_ORIGINS=http://localhost:3000
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=medreserve
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:`
  },

  // ===========================================
  // ENVIRONMENT VARIABLES TEMPLATE
  // ===========================================
  envTemplate: {
    // Copy this to your deployment platform's environment variables section
    variables: {
      // Required for Backend
      "DB_URL": "your-database-connection-string",
      "DB_USERNAME": "your-database-username", 
      "DB_PASSWORD": "your-database-password",
      "JWT_SECRET": "your-jwt-secret-key-minimum-32-characters",
      "CORS_ALLOWED_ORIGINS": "https://your-frontend-domain.com",
      
      // Required for Frontend
      "VITE_API_URL": "https://your-backend-domain.com",
      
      // Optional
      "JWT_EXPIRATION": "86400000",
      "MAIL_HOST": "smtp.gmail.com",
      "MAIL_PORT": "587",
      "MAIL_USERNAME": "<EMAIL>",
      "MAIL_PASSWORD": "your-app-password",
      "ML_SERVICE_URL": "https://your-ml-service.com",
      "CHATBOT_SERVICE_URL": "https://your-chatbot-service.com"
    }
  },

  // ===========================================
  // DEPLOYMENT INSTRUCTIONS
  // ===========================================
  instructions: {
    quickStart: [
      "1. Choose your deployment platform (Vercel, Netlify, Render, etc.)",
      "2. Set environment variables from envTemplate section",
      "3. Connect your GitHub repository",
      "4. Use the build commands from the respective platform section",
      "5. Deploy!"
    ],
    
    platforms: {
      vercel: "Copy vercel config to vercel.json, set env vars in dashboard",
      netlify: "Copy netlify config to netlify.toml, set env vars in dashboard", 
      render: "Copy render config to render.yaml, connect GitHub repo",
      railway: "Copy railway config to railway.toml, set env vars in dashboard",
      heroku: "Create Procfile with heroku config, set env vars via CLI",
      docker: "Use docker-compose.yml for local/cloud deployment"
    }
  }
};

// Export individual configurations for easy access
module.exports.getConfig = (platform, service) => {
  return module.exports[service]?.[platform] || module.exports[service];
};

// Generate platform-specific config files
module.exports.generateConfigFile = (platform, service) => {
  const config = module.exports.getConfig(platform, service);
  return JSON.stringify(config, null, 2);
};
