export const departments = [
  {
    id: 1,
    name: "Cardiology",
    description: "Comprehensive heart and cardiovascular care with state-of-the-art diagnostic and treatment facilities.",
    icon: "favorite",
    doctorCount: 5,
    services: [
      "Cardiac Catheterization",
      "Echocardiography",
      "Stress Testing",
      "Pacemaker Implantation",
      "Heart Surgery",
      "Preventive Cardiology"
    ],
    headOfDepartment: "Dr. <PERSON><PERSON>",
    location: "Building A, Floor 3",
    contactNumber: "+91-9876543210",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 9 AM - 6 PM"
  },
  {
    id: 2,
    name: "Neurology",
    description: "Advanced neurological care for brain, spine, and nervous system disorders with cutting-edge technology.",
    icon: "psychology",
    doctorCount: 5,
    services: [
      "Brain MRI & CT Scans",
      "EEG Testing",
      "Stroke Treatment",
      "Epilepsy Management",
      "Neurosurgery",
      "Rehabilitation Services"
    ],
    headOfDepartment: "Dr. <PERSON>",
    location: "Building B, Floor 2",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 8 AM - 5 PM"
  },
  {
    id: 3,
    name: "Dermatology",
    description: "Complete skin, hair, and nail care with cosmetic and medical dermatology services.",
    icon: "face",
    doctorCount: 5,
    services: [
      "Skin Cancer Screening",
      "Acne Treatment",
      "Cosmetic Procedures",
      "Hair Transplantation",
      "Laser Therapy",
      "Allergy Testing"
    ],
    headOfDepartment: "Dr. Lisa Anderson",
    location: "Building C, Floor 1",
    contactNumber: "+91-**********",
    emergencyAvailable: false,
    operatingHours: "OPD: 9 AM - 6 PM, Mon-Sat"
  },
  {
    id: 4,
    name: "Orthopedics",
    description: "Comprehensive bone, joint, and muscle care with advanced surgical and non-surgical treatments.",
    icon: "healing",
    doctorCount: 5,
    services: [
      "Joint Replacement",
      "Sports Medicine",
      "Fracture Treatment",
      "Arthroscopy",
      "Spine Surgery",
      "Physical Therapy"
    ],
    headOfDepartment: "Dr. James Taylor",
    location: "Building A, Floor 2",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 8 AM - 7 PM"
  },
  {
    id: 5,
    name: "Pediatrics",
    description: "Specialized healthcare for infants, children, and adolescents with child-friendly environment.",
    icon: "child_care",
    doctorCount: 5,
    services: [
      "Newborn Care",
      "Vaccination Programs",
      "Growth Monitoring",
      "Pediatric Surgery",
      "Child Psychology",
      "Emergency Pediatrics"
    ],
    headOfDepartment: "Dr. Maria Garcia",
    location: "Building D, Floor 1",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 9 AM - 6 PM"
  },
  {
    id: 6,
    name: "Psychiatry",
    description: "Mental health and behavioral wellness services with compassionate and professional care.",
    icon: "psychology_alt",
    doctorCount: 5,
    services: [
      "Depression Treatment",
      "Anxiety Management",
      "Addiction Counseling",
      "Family Therapy",
      "Cognitive Behavioral Therapy",
      "Psychiatric Evaluation"
    ],
    headOfDepartment: "Dr. Elena Rodriguez",
    location: "Building E, Floor 2",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Crisis Support, OPD: 9 AM - 8 PM"
  },
  {
    id: 7,
    name: "Oncology",
    description: "Comprehensive cancer care with advanced treatment options and supportive care services.",
    icon: "medical_services",
    doctorCount: 5,
    services: [
      "Chemotherapy",
      "Radiation Therapy",
      "Surgical Oncology",
      "Immunotherapy",
      "Palliative Care",
      "Cancer Screening"
    ],
    headOfDepartment: "Dr. Priya Sharma",
    location: "Building F, Floor 3",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 8 AM - 6 PM"
  },
  {
    id: 8,
    name: "Gynecology",
    description: "Women's health services including reproductive health, pregnancy care, and gynecological surgery.",
    icon: "pregnant_woman",
    doctorCount: 5,
    services: [
      "Prenatal Care",
      "Gynecological Surgery",
      "Family Planning",
      "Menopause Management",
      "Fertility Treatment",
      "Women's Health Screening"
    ],
    headOfDepartment: "Dr. Sophia Williams",
    location: "Building G, Floor 2",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 9 AM - 7 PM"
  },
  {
    id: 9,
    name: "Ophthalmology",
    description: "Complete eye care services with advanced diagnostic and surgical capabilities.",
    icon: "visibility",
    doctorCount: 5,
    services: [
      "Cataract Surgery",
      "Retinal Treatment",
      "Glaucoma Management",
      "LASIK Surgery",
      "Pediatric Ophthalmology",
      "Emergency Eye Care"
    ],
    headOfDepartment: "Dr. Alexander Kumar",
    location: "Building H, Floor 1",
    contactNumber: "+91-**********",
    emergencyAvailable: true,
    operatingHours: "24/7 Emergency, OPD: 8 AM - 6 PM"
  },
  {
    id: 10,
    name: "ENT",
    description: "Ear, nose, and throat care with advanced surgical and non-surgical treatment options.",
    icon: "hearing",
    doctorCount: 5,
    services: [
      "Hearing Tests",
      "Sinus Surgery",
      "Throat Surgery",
      "Allergy Treatment",
      "Voice Therapy",
      "Sleep Apnea Treatment"
    ],
    headOfDepartment: "Dr. Ahmed Hassan",
    location: "Building I, Floor 2",
    contactNumber: "+91-9876543219",
    emergencyAvailable: false,
    operatingHours: "OPD: 9 AM - 6 PM, Mon-Sat"
  },
  {
    id: 11,
    name: "Endocrinology",
    description: "Hormone and metabolic disorder treatment including diabetes care and thyroid management.",
    icon: "science",
    doctorCount: 5,
    services: [
      "Diabetes Management",
      "Thyroid Treatment",
      "Hormone Therapy",
      "Metabolic Disorders",
      "Osteoporosis Treatment",
      "Endocrine Surgery"
    ],
    headOfDepartment: "Dr. Rajesh Patel",
    location: "Building J, Floor 1",
    contactNumber: "+91-9876543220",
    emergencyAvailable: false,
    operatingHours: "OPD: 8 AM - 5 PM, Mon-Fri"
  }
];

export const getDepartmentByName = (name) => {
  return departments.find(dept => dept.name.toLowerCase() === name.toLowerCase());
};

export const getDepartmentsByEmergency = (emergencyAvailable) => {
  return departments.filter(dept => dept.emergencyAvailable === emergencyAvailable);
};

export const getTotalDoctors = () => {
  return departments.reduce((total, dept) => total + dept.doctorCount, 0);
};

export const getTotalDepartments = () => {
  return departments.length;
};
