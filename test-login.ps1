# Test Login Script for MedReserve Backend
# Tests the authentication endpoints with demo credentials

$backendUrl = "https://medreserve-ai-backend.onrender.com"
$frontendOrigin = "https://rishith2903.github.io"

Write-Host "🧪 Testing MedReserve Backend Authentication" -ForegroundColor Cyan
Write-Host "=" * 50

# Test 1: Health Check
Write-Host "`n🔍 Testing Health Endpoint..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "$backendUrl/actuator/health" -UseBasicParsing
    Write-Host "✅ Health Check: $($healthResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Demo Patient Login
Write-Host "`n🔍 Testing Demo Patient Login..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
    "Origin" = $frontendOrigin
}

try {
    $loginResponse = Invoke-WebRequest -Uri "$backendUrl/auth/login" -Method POST -Body $loginData -Headers $headers -UseBasicParsing
    Write-Host "✅ Patient Login: $($loginResponse.StatusCode)" -ForegroundColor Green
    
    # Parse response
    $responseData = $loginResponse.Content | ConvertFrom-Json
    Write-Host "   User: $($responseData.firstName) $($responseData.lastName)" -ForegroundColor Gray
    Write-Host "   Role: $($responseData.role)" -ForegroundColor Gray
    Write-Host "   Token: $($responseData.accessToken.Substring(0, 20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Patient Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "   Error Details: $errorBody" -ForegroundColor Red
    }
}

# Test 3: Demo Doctor Login
Write-Host "`n🔍 Testing Demo Doctor Login..." -ForegroundColor Yellow
$doctorLoginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $doctorResponse = Invoke-WebRequest -Uri "$backendUrl/auth/login" -Method POST -Body $doctorLoginData -Headers $headers -UseBasicParsing
    Write-Host "✅ Doctor Login: $($doctorResponse.StatusCode)" -ForegroundColor Green
    
    $doctorData = $doctorResponse.Content | ConvertFrom-Json
    Write-Host "   User: $($doctorData.firstName) $($doctorData.lastName)" -ForegroundColor Gray
    Write-Host "   Role: $($doctorData.role)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Doctor Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "   Error Details: $errorBody" -ForegroundColor Red
    }
}

# Test 4: Invalid Login
Write-Host "`n🔍 Testing Invalid Login..." -ForegroundColor Yellow
$invalidLoginData = @{
    email = "<EMAIL>"
    password = "wrongpassword"
} | ConvertTo-Json

try {
    $invalidResponse = Invoke-WebRequest -Uri "$backendUrl/auth/login" -Method POST -Body $invalidLoginData -Headers $headers -UseBasicParsing
    Write-Host "⚠️ Invalid Login Unexpectedly Succeeded: $($invalidResponse.StatusCode)" -ForegroundColor Yellow
} catch {
    Write-Host "✅ Invalid Login Properly Rejected: $($_.Exception.Response.StatusCode)" -ForegroundColor Green
}

# Test 5: Signup Test
Write-Host "`n🔍 Testing Signup..." -ForegroundColor Yellow
$signupData = @{
    firstName = "Test"
    lastName = "User"
    email = "testuser$(Get-Random)@test.com"
    password = "TestPassword123!"
    role = "PATIENT"
} | ConvertTo-Json

try {
    $signupResponse = Invoke-WebRequest -Uri "$backendUrl/auth/signup" -Method POST -Body $signupData -Headers $headers -UseBasicParsing
    Write-Host "✅ Signup: $($signupResponse.StatusCode)" -ForegroundColor Green
    
    $signupResult = $signupResponse.Content | ConvertFrom-Json
    Write-Host "   Message: $($signupResult.message)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Signup Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "   Error Details: $errorBody" -ForegroundColor Red
    }
}

Write-Host "`n📊 Test Summary Complete" -ForegroundColor Cyan
Write-Host "=" * 50
