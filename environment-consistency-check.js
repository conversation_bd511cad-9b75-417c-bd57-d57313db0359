const fs = require('fs');
const path = require('path');

console.log('🔧 MedReserve Environment Variables Consistency Check');
console.log('=' .repeat(70));

// Analysis results
const analysisResults = {
  frontendEnv: { status: 'unknown', details: null },
  backendEnv: { status: 'unknown', details: null },
  consistency: { status: 'unknown', details: null },
  deployment: { status: 'unknown', details: null }
};

async function checkEnvironmentConsistency() {
  
  // 1. Analyze Frontend Environment Variables
  console.log('\n1️⃣ Analyzing Frontend Environment Variables...');
  try {
    const frontendEnv = await analyzeFrontendEnvironment();
    analysisResults.frontendEnv.status = 'success';
    analysisResults.frontendEnv.details = frontendEnv;
    console.log('✅ Frontend environment analysis complete');
    console.log(`   - Found ${frontendEnv.local.length} local variables`);
    console.log(`   - Found ${frontendEnv.production.length} production variables`);
  } catch (error) {
    analysisResults.frontendEnv.status = 'error';
    analysisResults.frontendEnv.details = { error: error.message };
    console.log(`❌ Frontend environment analysis failed: ${error.message}`);
  }

  // 2. Analyze Backend Environment Variables
  console.log('\n2️⃣ Analyzing Backend Environment Variables...');
  try {
    const backendEnv = await analyzeBackendEnvironment();
    analysisResults.backendEnv.status = 'success';
    analysisResults.backendEnv.details = backendEnv;
    console.log('✅ Backend environment analysis complete');
    console.log(`   - Found ${backendEnv.local.length} local variables`);
    console.log(`   - Found ${backendEnv.production.length} production variables`);
  } catch (error) {
    analysisResults.backendEnv.status = 'error';
    analysisResults.backendEnv.details = { error: error.message };
    console.log(`❌ Backend environment analysis failed: ${error.message}`);
  }

  // 3. Check Cross-Service Consistency
  console.log('\n3️⃣ Checking Cross-Service Consistency...');
  try {
    const consistencyIssues = await checkCrossServiceConsistency();
    analysisResults.consistency.status = consistencyIssues.length === 0 ? 'success' : 'issues_found';
    analysisResults.consistency.details = consistencyIssues;
    
    if (consistencyIssues.length === 0) {
      console.log('✅ All environment variables are consistent across services');
    } else {
      console.log(`⚠️ Found ${consistencyIssues.length} consistency issues`);
      consistencyIssues.slice(0, 5).forEach(issue => {
        console.log(`   - ${issue.variable}: ${issue.issue}`);
      });
      if (consistencyIssues.length > 5) {
        console.log(`   ... and ${consistencyIssues.length - 5} more issues`);
      }
    }
  } catch (error) {
    analysisResults.consistency.status = 'error';
    analysisResults.consistency.details = { error: error.message };
    console.log(`❌ Consistency check failed: ${error.message}`);
  }

  // 4. Validate Deployment Configuration
  console.log('\n4️⃣ Validating Deployment Configuration...');
  try {
    const deploymentIssues = await validateDeploymentConfig();
    analysisResults.deployment.status = deploymentIssues.length === 0 ? 'success' : 'issues_found';
    analysisResults.deployment.details = deploymentIssues;
    
    if (deploymentIssues.length === 0) {
      console.log('✅ All deployment configurations are valid');
    } else {
      console.log(`⚠️ Found ${deploymentIssues.length} deployment configuration issues`);
      deploymentIssues.forEach(issue => {
        console.log(`   - ${issue.file}: ${issue.issue}`);
      });
    }
  } catch (error) {
    analysisResults.deployment.status = 'error';
    analysisResults.deployment.details = { error: error.message };
    console.log(`❌ Deployment validation failed: ${error.message}`);
  }

  // Print Summary
  printEnvironmentSummary();
}

async function analyzeFrontendEnvironment() {
  const frontendEnv = {
    local: [],
    production: [],
    viteConfig: null
  };

  // Check .env.local
  const localEnvPath = 'frontend/.env.local';
  if (fs.existsSync(localEnvPath)) {
    const localContent = fs.readFileSync(localEnvPath, 'utf8');
    frontendEnv.local = parseEnvFile(localContent);
  }

  // Check .env.production
  const prodEnvPath = 'frontend/.env.production';
  if (fs.existsSync(prodEnvPath)) {
    const prodContent = fs.readFileSync(prodEnvPath, 'utf8');
    frontendEnv.production = parseEnvFile(prodContent);
  }

  // Check vite.config.js for environment variable usage
  const viteConfigPath = 'frontend/vite.config.js';
  if (fs.existsSync(viteConfigPath)) {
    const viteContent = fs.readFileSync(viteConfigPath, 'utf8');
    frontendEnv.viteConfig = analyzeViteConfig(viteContent);
  }

  return frontendEnv;
}

async function analyzeBackendEnvironment() {
  const backendEnv = {
    local: [],
    production: [],
    applicationYml: null
  };

  // Check application-local.yml
  const localYmlPath = 'backend/src/main/resources/application-local.yml';
  if (fs.existsSync(localYmlPath)) {
    const localContent = fs.readFileSync(localYmlPath, 'utf8');
    backendEnv.local = parseYamlEnvVars(localContent);
  }

  // Check application-production.yml
  const prodYmlPath = 'backend/src/main/resources/application-production.yml';
  if (fs.existsSync(prodYmlPath)) {
    const prodContent = fs.readFileSync(prodYmlPath, 'utf8');
    backendEnv.production = parseYamlEnvVars(prodContent);
  }

  // Check main application.yml
  const mainYmlPath = 'backend/src/main/resources/application.yml';
  if (fs.existsSync(mainYmlPath)) {
    const mainContent = fs.readFileSync(mainYmlPath, 'utf8');
    backendEnv.applicationYml = parseYamlEnvVars(mainContent);
  }

  return backendEnv;
}

function parseEnvFile(content) {
  const variables = [];
  const lines = content.split('\n');
  
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        variables.push({
          key: key.trim(),
          value: valueParts.join('=').trim(),
          hasValue: valueParts.join('=').trim().length > 0
        });
      }
    }
  }
  
  return variables;
}

function parseYamlEnvVars(content) {
  const variables = [];

  try {
    // Look for ${VAR_NAME} patterns in YAML content
    const envVarPattern = /\$\{([^}]+)\}/g;
    let match;

    while ((match = envVarPattern.exec(content)) !== null) {
      const varName = match[1];
      if (!variables.find(v => v.key === varName)) {
        variables.push({
          key: varName,
          value: '${' + varName + '}',
          hasValue: false,
          isReference: true
        });
      }
    }

    return variables;
  } catch (error) {
    console.warn(`Warning: Could not parse YAML content: ${error.message}`);
    return variables;
  }
}

function analyzeViteConfig(content) {
  const config = {
    envPrefix: null,
    defineVars: []
  };
  
  // Look for envPrefix configuration
  const envPrefixMatch = content.match(/envPrefix:\s*['"]([^'"]+)['"]/);
  if (envPrefixMatch) {
    config.envPrefix = envPrefixMatch[1];
  }
  
  // Look for define configuration
  const defineMatch = content.match(/define:\s*\{([^}]+)\}/);
  if (defineMatch) {
    const defineContent = defineMatch[1];
    const varMatches = defineContent.match(/['"]([^'"]+)['"]:\s*['"]([^'"]+)['"]/g);
    if (varMatches) {
      config.defineVars = varMatches.map(match => {
        const [key, value] = match.split(':').map(s => s.trim().replace(/['"]/g, ''));
        return { key, value };
      });
    }
  }
  
  return config;
}

async function checkCrossServiceConsistency() {
  const issues = [];
  
  if (analysisResults.frontendEnv.status !== 'success' || 
      analysisResults.backendEnv.status !== 'success') {
    return [{ variable: 'N/A', issue: 'Cannot check consistency due to environment analysis failures' }];
  }
  
  const frontendEnv = analysisResults.frontendEnv.details;
  const backendEnv = analysisResults.backendEnv.details;
  
  // Check if frontend and backend service URLs are consistent
  const frontendServiceUrls = [
    ...frontendEnv.local.filter(v => v.key.includes('API_URL') || v.key.includes('SERVICE_URL')),
    ...frontendEnv.production.filter(v => v.key.includes('API_URL') || v.key.includes('SERVICE_URL'))
  ];
  
  const backendServiceUrls = [
    ...backendEnv.local.filter(v => v.key.includes('URL') || v.key.includes('HOST')),
    ...backendEnv.production.filter(v => v.key.includes('URL') || v.key.includes('HOST'))
  ];
  
  // Check for common environment variables that should be consistent
  const commonVars = ['JWT_SECRET', 'DATABASE_URL', 'CORS_ALLOWED_ORIGINS'];
  
  for (const varName of commonVars) {
    const frontendVar = frontendServiceUrls.find(v => v.key === varName);
    const backendVar = backendServiceUrls.find(v => v.key === varName);
    
    if (frontendVar && backendVar && frontendVar.value !== backendVar.value) {
      issues.push({
        variable: varName,
        issue: `Mismatch between frontend (${frontendVar.value}) and backend (${backendVar.value})`
      });
    }
  }
  
  // Check if all required service URLs are present
  const requiredFrontendVars = ['VITE_API_BASE_URL', 'VITE_ML_SERVICE_URL', 'VITE_CHATBOT_SERVICE_URL'];
  
  for (const varName of requiredFrontendVars) {
    const localVar = frontendEnv.local.find(v => v.key === varName);
    const prodVar = frontendEnv.production.find(v => v.key === varName);
    
    if (!localVar) {
      issues.push({
        variable: varName,
        issue: 'Missing from frontend local environment'
      });
    }
    
    if (!prodVar) {
      issues.push({
        variable: varName,
        issue: 'Missing from frontend production environment'
      });
    }
    
    if (localVar && !localVar.hasValue) {
      issues.push({
        variable: varName,
        issue: 'Empty value in frontend local environment'
      });
    }
    
    if (prodVar && !prodVar.hasValue) {
      issues.push({
        variable: varName,
        issue: 'Empty value in frontend production environment'
      });
    }
  }
  
  return issues;
}

async function validateDeploymentConfig() {
  const issues = [];
  
  // Check GitHub Pages deployment configuration
  const packageJsonPath = 'frontend/package.json';
  if (fs.existsSync(packageJsonPath)) {
    try {
      const packageContent = fs.readFileSync(packageJsonPath, 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      if (!packageJson.homepage) {
        issues.push({
          file: packageJsonPath,
          issue: 'Missing homepage field for GitHub Pages deployment'
        });
      }
      
      if (!packageJson.scripts || !packageJson.scripts.deploy) {
        issues.push({
          file: packageJsonPath,
          issue: 'Missing deploy script for GitHub Pages'
        });
      }
    } catch (error) {
      issues.push({
        file: packageJsonPath,
        issue: `Failed to parse package.json: ${error.message}`
      });
    }
  }
  
  // Check Render deployment configuration
  const renderYamlPath = 'render.yaml';
  if (fs.existsSync(renderYamlPath)) {
    try {
      const renderContent = fs.readFileSync(renderYamlPath, 'utf8');

      // Simple check for services section
      if (!renderContent.includes('services:')) {
        issues.push({
          file: renderYamlPath,
          issue: 'No services section found in render.yaml'
        });
      }
    } catch (error) {
      issues.push({
        file: renderYamlPath,
        issue: `Failed to read render.yaml: ${error.message}`
      });
    }
  } else {
    issues.push({
      file: renderYamlPath,
      issue: 'Missing render.yaml for Render deployment'
    });
  }
  
  return issues;
}

function printEnvironmentSummary() {
  console.log('\n📊 ENVIRONMENT CONSISTENCY SUMMARY');
  console.log('=' .repeat(70));
  
  const results = Object.entries(analysisResults);
  let successCount = 0;
  let totalCount = 0;
  
  for (const [category, result] of results) {
    const icon = result.status === 'success' ? '✅' : 
                 result.status === 'issues_found' ? '⚠️' : '❌';
    const categoryName = category.replace(/([A-Z])/g, ' $1').trim().toUpperCase();
    
    console.log(`${icon} ${categoryName}: ${result.status}`);
    
    if (result.status === 'success') successCount++;
    if (result.status !== 'skipped') totalCount++;
  }
  
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (analysisResults.frontendEnv.status === 'error') {
    console.log('⚠️ Frontend environment analysis failed - check .env files');
  }
  
  if (analysisResults.backendEnv.status === 'error') {
    console.log('⚠️ Backend environment analysis failed - check application.yml files');
  }
  
  if (analysisResults.consistency.status === 'issues_found') {
    console.log('⚠️ Environment variable inconsistencies detected - align service configurations');
  }
  
  if (analysisResults.deployment.status === 'issues_found') {
    console.log('⚠️ Deployment configuration issues - fix deployment settings');
  }
  
  console.log(`\n🎯 Overall Environment Score: ${successCount}/${totalCount} categories passed`);
  
  if (successCount === totalCount) {
    console.log('🎉 Environment configuration is consistent and properly set up!');
    console.log('✅ All services should communicate correctly with current configuration');
  } else {
    console.log('⚠️ Environment configuration issues detected. Review the issues above.');
  }
}

// Run the environment consistency check
checkEnvironmentConsistency().catch(console.error);
