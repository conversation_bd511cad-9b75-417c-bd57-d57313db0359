server:
  port: ${PORT:8082}
  servlet:
    context-path: /

spring:
  application:
    name: medreserve-chatbot
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  # Jackson configuration for JSON processing
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Logging configuration
logging:
  level:
    com.medreserve.chatbot: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management endpoints for health checks
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Chatbot configuration
chatbot:
  supported-languages:
    - en
    - hi
    - te
  default-language: en
  session-timeout: 30m
  
# CORS configuration
cors:
  allowed-origins: "*"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  allow-credentials: false

---
# Development profile
spring:
  config:
    activate:
      on-profile: dev
      
logging:
  level:
    com.medreserve.chatbot: DEBUG
    org.springframework.web: DEBUG

chatbot:
  debug-mode: true

---
# Production profile  
spring:
  config:
    activate:
      on-profile: prod

server:
  port: ${PORT:8080}

logging:
  level:
    com.medreserve.chatbot: INFO
    org.springframework.web: WARN
    
chatbot:
  debug-mode: false

cors:
  allowed-origins: 
    - "https://rishith2903.github.io"
    - "https://medreserve-ai-backend.onrender.com"
