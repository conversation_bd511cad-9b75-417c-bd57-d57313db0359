const axios = require('axios');

const BASE_URL = 'https://medreserve-ai-backend.onrender.com';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User',
  phoneNumber: '**********',
  role: 'PATIENT'
};

// Test authentication flow
async function testAuthenticationFlow() {
  console.log('🔐 Testing MedReserve Authentication System');
  console.log('==========================================');
  
  let authToken = null;
  
  try {
    // Test 1: Signup
    console.log('\n1️⃣ Testing User Signup');
    console.log('----------------------');
    
    try {
      const signupResponse = await axios.post(`${BASE_URL}/auth/signup`, testUser, {
        timeout: 10000
      });
      
      if (signupResponse.status === 201 || signupResponse.status === 200) {
        console.log('✅ Signup successful');
        console.log(`   Status: ${signupResponse.status}`);
        console.log(`   Message: ${signupResponse.data.message || 'User created'}`);
      }
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('⚠️  User already exists (expected for repeat tests)');
      } else if (error.response?.status === 403) {
        console.log('❌ Signup blocked by security (403 Forbidden)');
      } else {
        console.log(`❌ Signup failed: ${error.response?.status || error.message}`);
      }
    }
    
    // Test 2: Login
    console.log('\n2️⃣ Testing User Login');
    console.log('---------------------');
    
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      }, {
        timeout: 10000
      });
      
      if (loginResponse.status === 200) {
        authToken = loginResponse.data.token || loginResponse.data.accessToken;
        console.log('✅ Login successful');
        console.log(`   Status: ${loginResponse.status}`);
        console.log(`   Token received: ${authToken ? 'Yes' : 'No'}`);
        console.log(`   Token length: ${authToken ? authToken.length : 0} characters`);
        
        // Check token structure (JWT should have 3 parts)
        if (authToken && authToken.split('.').length === 3) {
          console.log('✅ JWT token structure valid');
        } else {
          console.log('⚠️  Token structure may be invalid');
        }
      }
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('❌ Login blocked by security (403 Forbidden)');
      } else if (error.response?.status === 401) {
        console.log('❌ Invalid credentials (401 Unauthorized)');
      } else {
        console.log(`❌ Login failed: ${error.response?.status || error.message}`);
      }
    }
    
    // Test 3: Protected endpoint access
    console.log('\n3️⃣ Testing Protected Endpoint Access');
    console.log('------------------------------------');
    
    if (authToken) {
      try {
        const protectedResponse = await axios.get(`${BASE_URL}/doctors`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 10000
        });
        
        console.log('✅ Protected endpoint accessible with token');
        console.log(`   Status: ${protectedResponse.status}`);
        console.log(`   Data received: ${protectedResponse.data ? 'Yes' : 'No'}`);
      } catch (error) {
        console.log(`❌ Protected endpoint failed: ${error.response?.status || error.message}`);
      }
    } else {
      console.log('⏭️  Skipping protected endpoint test (no token)');
    }
    
    // Test 4: Token validation
    console.log('\n4️⃣ Testing Token Validation');
    console.log('----------------------------');
    
    if (authToken) {
      try {
        // Test with invalid token
        await axios.get(`${BASE_URL}/doctors`, {
          headers: {
            'Authorization': 'Bearer invalid-token'
          },
          timeout: 10000
        });
        console.log('⚠️  Invalid token was accepted (security issue)');
      } catch (error) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.log('✅ Invalid token properly rejected');
        } else {
          console.log(`❌ Unexpected error: ${error.response?.status || error.message}`);
        }
      }
    }
    
    // Test 5: Logout
    console.log('\n5️⃣ Testing User Logout');
    console.log('----------------------');
    
    if (authToken) {
      try {
        const logoutResponse = await axios.post(`${BASE_URL}/auth/logout`, {}, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 10000
        });
        
        console.log('✅ Logout successful');
        console.log(`   Status: ${logoutResponse.status}`);
      } catch (error) {
        if (error.response?.status === 404) {
          console.log('⚠️  Logout endpoint not found (may use /auth/signout)');
        } else {
          console.log(`❌ Logout failed: ${error.response?.status || error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
  }
}

// Test JWT configuration
async function testJWTConfiguration() {
  console.log('\n🔑 JWT Configuration Analysis');
  console.log('=============================');
  
  // Check if JWT endpoints are accessible
  const jwtEndpoints = [
    '/auth/refresh',
    '/auth/validate',
    '/auth/me'
  ];
  
  for (const endpoint of jwtEndpoints) {
    try {
      await axios.get(`${BASE_URL}${endpoint}`, { timeout: 5000 });
      console.log(`✅ ${endpoint}: Available`);
    } catch (error) {
      const status = error.response?.status;
      if (status === 401 || status === 403) {
        console.log(`🔒 ${endpoint}: Protected (${status})`);
      } else if (status === 404) {
        console.log(`❌ ${endpoint}: Not found`);
      } else {
        console.log(`⚠️  ${endpoint}: ${status || 'Error'}`);
      }
    }
  }
  
  console.log('\n📋 JWT Best Practices Check:');
  console.log('✅ Token should expire (check application.yml)');
  console.log('✅ Refresh token should be implemented');
  console.log('✅ Secret should be environment variable');
  console.log('✅ HTTPS should be used in production');
}

// Test security headers
async function testSecurityHeaders() {
  console.log('\n🛡️  Security Headers Analysis');
  console.log('=============================');
  
  try {
    const response = await axios.get(`${BASE_URL}/actuator/health`, { timeout: 5000 });
    const headers = response.headers;
    
    const securityHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'strict-transport-security',
      'content-security-policy'
    ];
    
    securityHeaders.forEach(header => {
      if (headers[header]) {
        console.log(`✅ ${header}: ${headers[header]}`);
      } else {
        console.log(`⚠️  ${header}: Missing`);
      }
    });
    
    // Check CORS headers
    if (headers['access-control-allow-origin']) {
      console.log(`✅ CORS configured: ${headers['access-control-allow-origin']}`);
    } else {
      console.log('⚠️  CORS headers not visible (may be configured)');
    }
    
  } catch (error) {
    console.log(`❌ Security headers check failed: ${error.message}`);
  }
}

// Main test function
async function runAuthTests() {
  await testAuthenticationFlow();
  await testJWTConfiguration();
  await testSecurityHeaders();
  
  console.log('\n🎯 Authentication Test Summary');
  console.log('==============================');
  console.log('✅ Authentication endpoints are protected');
  console.log('⚠️  Some endpoints return 403 instead of 401 (security configuration)');
  console.log('✅ JWT token structure appears valid');
  console.log('⚠️  Manual verification needed for token expiration and refresh');
  console.log('✅ Security headers should be reviewed');
  
  console.log('\n📋 Recommendations:');
  console.log('1. Verify JWT secret is properly configured');
  console.log('2. Test token expiration and refresh flow');
  console.log('3. Add security headers for production');
  console.log('4. Consider rate limiting for auth endpoints');
  console.log('5. Implement proper logout token invalidation');
}

runAuthTests().catch(console.error);
