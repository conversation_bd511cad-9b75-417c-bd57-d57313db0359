[build]
  base = "frontend/"
  command = "npm run build"
  publish = "dist/"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production]
  [context.production.environment]
    VITE_API_URL = "https://your-backend-url.onrender.com"

[context.deploy-preview]
  [context.deploy-preview.environment]
    VITE_API_URL = "https://your-backend-url.onrender.com"

[context.branch-deploy]
  [context.branch-deploy.environment]
    VITE_API_URL = "https://your-backend-url.onrender.com"
