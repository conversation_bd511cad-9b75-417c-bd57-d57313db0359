services:
  - type: web
    name: medreserve-backend
    env: docker
    plan: free
    autoDeploy: true
    region: singapore
    healthCheckPath: /api/api/actuator/health
    dockerfilePath: ./Dockerfile
    envVars:
      - key: SPRING_PROFILES_ACTIVE
        value: production
      - key: DB_URL
        value: ************************************************************************************************
      - key: DB_USERNAME
        value: medreserve_user
      - key: DB_PASSWORD
        value: your_database_password_here
      - key: MAIL_USERNAME
        value: <EMAIL>
      - key: MAIL_PASSWORD
        value: your_gmail_app_password
      - key: JWT_SECRET
        value: mySecretKey123456789012345678901234567890
      - key: CORS_ALLOWED_ORIGINS
        value: https://rishith2903.github.io,http://localhost:3000
      - key: JWT_SECRET
        value: mySecretKey123456789012345678901234567890
      - key: JWT_EXPIRATION
        value: 86400000
      - key: JWT_REFRESH_EXPIRATION
        value: 604800000
      - key: ML_SERVICE_URL
        value: http://unavailable:8001
      - key: CHATBOT_SERVICE_URL
        value: http://unavailable:5005

# Database service (PostgreSQL)
databases:
  - name: medreserve-db
    databaseName: medreserve
    user: medreserve_user
    plan: free
