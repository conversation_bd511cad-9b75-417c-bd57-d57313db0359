#!/usr/bin/env node
/**
 * CORS Fix Verification Script
 * Tests CORS configuration for MedReserve backend
 */

const https = require('https');
const http = require('http');

const BACKEND_URL = 'https://medreserve-ai-backend.onrender.com';
const FRONTEND_ORIGIN = 'https://rishith2903.github.io';

/**
 * Test CORS preflight request
 */
async function testCORSPreflight(endpoint) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BACKEND_URL);
        
        const options = {
            hostname: url.hostname,
            port: url.port || 443,
            path: url.pathname,
            method: 'OPTIONS',
            headers: {
                'Origin': FRONTEND_ORIGIN,
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type,Authorization'
            }
        };

        const req = https.request(options, (res) => {
            const corsHeaders = {
                'Access-Control-Allow-Origin': res.headers['access-control-allow-origin'],
                'Access-Control-Allow-Methods': res.headers['access-control-allow-methods'],
                'Access-Control-Allow-Headers': res.headers['access-control-allow-headers'],
                'Access-Control-Allow-Credentials': res.headers['access-control-allow-credentials']
            };

            resolve({
                statusCode: res.statusCode,
                headers: corsHeaders,
                endpoint: endpoint
            });
        });

        req.on('error', (error) => {
            reject({ endpoint, error: error.message });
        });

        req.setTimeout(10000, () => {
            req.destroy();
            reject({ endpoint, error: 'Request timeout' });
        });

        req.end();
    });
}

/**
 * Test actual API request
 */
async function testAPIRequest(endpoint, method = 'GET', body = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BACKEND_URL);
        
        const options = {
            hostname: url.hostname,
            port: url.port || 443,
            path: url.pathname,
            method: method,
            headers: {
                'Origin': FRONTEND_ORIGIN,
                'Content-Type': 'application/json'
            }
        };

        if (body) {
            options.headers['Content-Length'] = Buffer.byteLength(JSON.stringify(body));
        }

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: {
                        'Access-Control-Allow-Origin': res.headers['access-control-allow-origin']
                    },
                    endpoint: endpoint,
                    method: method
                });
            });
        });

        req.on('error', (error) => {
            reject({ endpoint, method, error: error.message });
        });

        req.setTimeout(10000, () => {
            req.destroy();
            reject({ endpoint, method, error: 'Request timeout' });
        });

        if (body) {
            req.write(JSON.stringify(body));
        }
        req.end();
    });
}

/**
 * Main test function
 */
async function runCORSTests() {
    console.log('🧪 CORS Configuration Test for MedReserve Backend');
    console.log('=' .repeat(60));
    console.log(`Backend URL: ${BACKEND_URL}`);
    console.log(`Frontend Origin: ${FRONTEND_ORIGIN}`);
    console.log('');

    const endpoints = [
        '/auth/login',
        '/auth/signup',
        '/actuator/health',
        '/test/health'
    ];

    let passedTests = 0;
    let totalTests = 0;

    console.log('🔍 Testing CORS Preflight Requests:');
    console.log('-'.repeat(40));

    for (const endpoint of endpoints) {
        totalTests++;
        try {
            const result = await testCORSPreflight(endpoint);
            const allowOrigin = result.headers['Access-Control-Allow-Origin'];
            
            if (allowOrigin === FRONTEND_ORIGIN || allowOrigin === '*') {
                console.log(`✅ ${endpoint} - CORS OK (${result.statusCode})`);
                console.log(`   Origin: ${allowOrigin}`);
                passedTests++;
            } else {
                console.log(`❌ ${endpoint} - CORS FAILED (${result.statusCode})`);
                console.log(`   Expected: ${FRONTEND_ORIGIN}`);
                console.log(`   Got: ${allowOrigin || 'undefined'}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint} - ERROR: ${error.error}`);
        }
        console.log('');
    }

    console.log('🌐 Testing Actual API Requests:');
    console.log('-'.repeat(40));

    // Test health endpoint
    totalTests++;
    try {
        const result = await testAPIRequest('/actuator/health');
        const allowOrigin = result.headers['Access-Control-Allow-Origin'];
        
        if (allowOrigin === FRONTEND_ORIGIN || allowOrigin === '*') {
            console.log(`✅ GET /actuator/health - CORS OK (${result.statusCode})`);
            passedTests++;
        } else {
            console.log(`❌ GET /actuator/health - CORS FAILED (${result.statusCode})`);
            console.log(`   Origin header: ${allowOrigin || 'undefined'}`);
        }
    } catch (error) {
        console.log(`❌ GET /actuator/health - ERROR: ${error.error}`);
    }

    console.log('');
    console.log('📊 Test Summary:');
    console.log('=' .repeat(30));
    console.log(`Passed: ${passedTests}/${totalTests} tests`);
    console.log(`Success Rate: ${Math.round((passedTests/totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All CORS tests passed! Frontend should work correctly.');
    } else {
        console.log('⚠️  Some CORS tests failed. Check backend configuration.');
        console.log('');
        console.log('🔧 Troubleshooting:');
        console.log('1. Ensure CORS_ALLOWED_ORIGINS environment variable is set');
        console.log('2. Verify backend deployment includes updated SecurityConfig.java');
        console.log('3. Check that backend service is running and accessible');
    }

    return passedTests === totalTests;
}

// Run the tests
if (require.main === module) {
    runCORSTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Test execution failed:', error);
            process.exit(1);
        });
}

module.exports = { runCORSTests, testCORSPreflight, testAPIRequest };
