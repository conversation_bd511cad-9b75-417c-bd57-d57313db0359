services:
  # Frontend Service
  - type: web
    name: medreserve-ai-frontend
    env: static
    buildCommand: cd frontend && npm ci && npm run build
    staticPublishPath: frontend/dist
    envVars:
      - key: VITE_API_URL
        value: https://medreserve-ai-backend.onrender.com
    routes:
      - type: rewrite
        source: /*
        destination: /index.html

  # Backend Service  
  - type: web
    name: medreserve-ai-backend
    env: java
    region: oregon
    plan: free
    buildCommand: mvn clean package -DskipTests
    startCommand: java -jar target/medreserve-backend-0.0.1-SNAPSHOT.jar
    healthCheckPath: /actuator/health
    envVars:
      - key: SPRING_PROFILES_ACTIVE
        value: prod
      - key: PORT
        value: 8080
      - key: DB_URL
        fromDatabase:
          name: medreserve-db
          property: connectionString
      - key: DB_USERNAME
        fromDatabase:
          name: medreserve-db
          property: user
      - key: DB_PASSWORD
        fromDatabase:
          name: medreserve-db
          property: password
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRATION
        value: 86400000
      - key: CORS_ALLOWED_ORIGINS
        value: https://medreserve-ai-frontend.onrender.com

  # ML Service
  - type: web
    name: medreserve-ai-ml
    env: python
    region: oregon
    plan: free
    buildCommand: cd backend/ml && pip install -r requirements.txt
    startCommand: cd backend/ml && python app.py
    healthCheckPath: /health
    envVars:
      - key: PORT
        value: 5000
      - key: FLASK_ENV
        value: production

  # Chatbot Service
  - type: web
    name: medreserve-ai-chatbot
    env: java
    region: oregon
    plan: free
    buildCommand: cd backend/chatbot && mvn clean package -DskipTests
    startCommand: cd backend/chatbot && java -jar target/chatbot-service-0.0.1-SNAPSHOT.jar
    healthCheckPath: /actuator/health
    envVars:
      - key: SPRING_PROFILES_ACTIVE
        value: prod
      - key: PORT
        value: 8081

databases:
  - name: medreserve-db
    databaseName: medreserve
    user: medreserve_user
    region: oregon
    plan: free
