#!/bin/bash

# MedReserve Project Cleanup Script (Linux/Mac)
# This script removes unnecessary files and build artifacts to clean up the project

echo "🧹 MedReserve Project Cleanup Script"
echo "===================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Function to safely remove files/directories
remove_safely() {
    local path="$1"
    local description="$2"
    local force="$3"
    
    if [ -e "$path" ]; then
        if [ "$force" = "true" ]; then
            echo -e "${YELLOW}🗑️  Removing: $description${NC}"
            rm -rf "$path" 2>/dev/null
            if [ ! -e "$path" ]; then
                echo -e "   ${GREEN}✅ Removed successfully${NC}"
            else
                echo -e "   ${RED}❌ Failed to remove${NC}"
            fi
        else
            echo -n "Remove $description? (y/N): "
            read -r response
            if [ "$response" = "y" ] || [ "$response" = "Y" ]; then
                echo -e "${YELLOW}🗑️  Removing: $description${NC}"
                rm -rf "$path" 2>/dev/null
                if [ ! -e "$path" ]; then
                    echo -e "   ${GREEN}✅ Removed successfully${NC}"
                else
                    echo -e "   ${RED}❌ Failed to remove${NC}"
                fi
            else
                echo -e "   ${GRAY}⏭️  Skipped${NC}"
            fi
        fi
    else
        echo -e "   ${GRAY}ℹ️  Not found: $path${NC}"
    fi
}

# Get current directory
project_root=$(pwd)
echo -e "${BLUE}📁 Project Root: $project_root${NC}"
echo ""

# Ask for cleanup mode
echo -e "${CYAN}Choose cleanup mode:${NC}"
echo -e "${WHITE}1. Interactive (ask for each item)${NC}"
echo -e "${WHITE}2. Safe Auto (remove build artifacts only)${NC}"
echo -e "${WHITE}3. Full Auto (remove all unnecessary files)${NC}"
echo -n "Enter choice (1-3): "
read -r mode

auto_mode=false
full_auto=false

if [ "$mode" = "2" ] || [ "$mode" = "3" ]; then
    auto_mode=true
fi

if [ "$mode" = "3" ]; then
    full_auto=true
fi

echo ""
echo -e "${GREEN}🚀 Starting cleanup...${NC}"
echo ""

# 1. ROOT LEVEL CLEANUP
echo -e "${MAGENTA}📂 ROOT LEVEL CLEANUP${NC}"
echo -e "${MAGENTA}=====================${NC}"

remove_safely "./node_modules" "Root node_modules directory" "$auto_mode"
remove_safely "./package.json" "Root package.json" "$full_auto"
remove_safely "./package-lock.json" "Root package-lock.json" "$full_auto"

# 2. TEMPORARY TEST FILES
echo ""
echo -e "${MAGENTA}🧪 TEMPORARY TEST FILES${NC}"
echo -e "${MAGENTA}=======================${NC}"

remove_safely "./comprehensive-api-test.js" "Comprehensive API test file" "$full_auto"
remove_safely "./test-disease-prediction.js" "Disease prediction test file" "$full_auto"
remove_safely "./test-integration.js" "Integration test file" "$full_auto"
remove_safely "./test-multilingual-chatbot.js" "Multilingual chatbot test file" "$full_auto"

# 3. DUPLICATE DOCUMENTATION
echo ""
echo -e "${MAGENTA}📄 DUPLICATE DOCUMENTATION${NC}"
echo -e "${MAGENTA}===========================${NC}"

remove_safely "./DEPLOYMENT_CHECKLIST.md" "Deployment checklist" "$full_auto"
remove_safely "./LOCAL_DEVELOPMENT.md" "Local development guide" "$full_auto"
remove_safely "./MULTILINGUAL_CHATBOT_INTEGRATION_COMPLETE.md" "Chatbot integration doc" "$full_auto"
remove_safely "./QUICK_START.md" "Quick start guide" "$full_auto"
remove_safely "./START_HERE.md" "Start here guide" "$full_auto"
remove_safely "./COMPREHENSIVE_ANALYSIS_REPORT.md" "Analysis report" "$full_auto"

# 4. BUILD ARTIFACTS
echo ""
echo -e "${MAGENTA}🏗️  BUILD ARTIFACTS${NC}"
echo -e "${MAGENTA}===================${NC}"

remove_safely "./backend/target" "Maven target directory" "$auto_mode"
remove_safely "./backend/logs" "Backend log files" "$auto_mode"
remove_safely "./frontend/build" "Frontend build directory" "$auto_mode"
remove_safely "./frontend/dist" "Frontend dist directory" "$auto_mode"
remove_safely "./frontend/node_modules" "Frontend node_modules" "$auto_mode"

# 5. TEMPORARY SCRIPTS
echo ""
echo -e "${MAGENTA}🔧 TEMPORARY SCRIPTS${NC}"
echo -e "${MAGENTA}====================${NC}"

remove_safely "./start-local.ps1" "Local start script" "$full_auto"
remove_safely "./start-medreserve-with-chatbot.bat" "Start with chatbot script" "$full_auto"
remove_safely "./start-medreserve.bat" "Start medreserve script" "$full_auto"
remove_safely "./start-medreserve.sh" "Start medreserve shell script" "$full_auto"
remove_safely "./backend/test-deployment.sh" "Backend test deployment script" "$full_auto"

# 6. DUPLICATE DEPLOYMENT FILES
echo ""
echo -e "${MAGENTA}🐳 DEPLOYMENT FILES REVIEW${NC}"
echo -e "${MAGENTA}===========================${NC}"

remove_safely "./docker-compose.local.yml" "Local docker compose file" "$full_auto"
remove_safely "./frontend/Dockerfile.dev" "Frontend dev Dockerfile" "$full_auto"
remove_safely "./backend/README_DEPLOYMENT.md" "Backend deployment README" "$full_auto"
remove_safely "./frontend/DEPLOY.md" "Frontend deploy guide" "$full_auto"
remove_safely "./frontend/PROJECT_STRUCTURE.md" "Frontend project structure" "$full_auto"

# 7. PLATFORM-SPECIFIC FILES (Optional)
echo ""
echo -e "${MAGENTA}🌐 PLATFORM-SPECIFIC FILES${NC}"
echo -e "${MAGENTA}===========================${NC}"
echo -e "${YELLOW}⚠️  Only remove if you're not using these platforms:${NC}"

if [ "$auto_mode" = "false" ]; then
    remove_safely "./backend/nixpacks.toml" "Nixpacks config (remove if not using Nixpacks)" "false"
    remove_safely "./backend/railway.json" "Railway config (remove if not using Railway)" "false"
    remove_safely "./backend/Procfile" "Heroku Procfile (remove if not using Heroku)" "false"
fi

echo ""
echo -e "${GREEN}✨ CLEANUP SUMMARY${NC}"
echo -e "${GREEN}==================${NC}"

# Check important files
important_files=(
    "README.md"
    "backend/pom.xml"
    "backend/src"
    "backend/render.yaml"
    "frontend/package.json"
    "frontend/src"
    "frontend/vite.config.js"
)

echo -e "${GREEN}✅ Important files preserved:${NC}"
for file in "${important_files[@]}"; do
    if [ -e "$file" ]; then
        echo -e "   ${GREEN}✓ $file${NC}"
    else
        echo -e "   ${YELLOW}⚠️  $file (not found)${NC}"
    fi
done

echo ""
echo -e "${GREEN}🎉 Cleanup completed!${NC}"
echo ""
echo -e "${CYAN}📋 Next steps:${NC}"
echo -e "${WHITE}1. Run 'cd frontend && npm install' to reinstall frontend dependencies${NC}"
echo -e "${WHITE}2. Run 'cd backend && mvn clean compile' to rebuild backend${NC}"
echo -e "${WHITE}3. Test the application to ensure everything works${NC}"
echo ""
echo -e "${YELLOW}💡 Tip: Add these patterns to .gitignore to prevent future clutter:${NC}"
echo -e "${GRAY}   - node_modules/${NC}"
echo -e "${GRAY}   - target/${NC}"
echo -e "${GRAY}   - dist/${NC}"
echo -e "${GRAY}   - build/${NC}"
echo -e "${GRAY}   - logs/${NC}"
echo ""

# Offer to create .gitignore
if [ "$auto_mode" = "false" ]; then
    echo -n "Would you like to update .gitignore with recommended patterns? (y/N): "
    read -r create_gitignore
    if [ "$create_gitignore" = "y" ] || [ "$create_gitignore" = "Y" ]; then
        echo -e "${BLUE}📝 Updating .gitignore...${NC}"
        echo -e "   ${GRAY}ℹ️  Please manually add the recommended patterns to .gitignore${NC}"
    fi
fi

echo -e "${GREEN}🏁 Script completed successfully!${NC}"
