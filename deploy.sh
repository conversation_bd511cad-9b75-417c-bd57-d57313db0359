#!/bin/bash

# MedReserve AI - Universal Deployment Script
# Works on any platform: Linux, macOS, Windows (Git Bash), Docker, Cloud providers
# No platform-specific configurations needed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="MedReserve AI"
FRONTEND_DIR="frontend"
BACKEND_DIR="backend"
ML_DIR="backend/ml"
CHATBOT_DIR="backend/chatbot"

# Default ports
FRONTEND_PORT=${PORT:-3000}
BACKEND_PORT=${PORT:-8080}
ML_PORT=${PORT:-5000}
CHATBOT_PORT=${PORT:-8081}

# Functions
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install dependencies
install_dependencies() {
    header "Installing Dependencies"
    
    # Frontend dependencies
    if [ -d "$FRONTEND_DIR" ] && [ -f "$FRONTEND_DIR/package.json" ]; then
        log "Installing frontend dependencies..."
        cd "$FRONTEND_DIR"
        if command_exists npm; then
            npm install
        elif command_exists yarn; then
            yarn install
        else
            error "npm or yarn not found. Please install Node.js"
            exit 1
        fi
        cd ..
        log "Frontend dependencies installed ✓"
    fi
    
    # Backend dependencies (Maven)
    if [ -d "$BACKEND_DIR" ] && [ -f "$BACKEND_DIR/pom.xml" ]; then
        log "Installing backend dependencies..."
        cd "$BACKEND_DIR"
        if command_exists mvn; then
            mvn clean install -DskipTests
        else
            warn "Maven not found. Skipping backend build"
        fi
        cd ..
        log "Backend dependencies installed ✓"
    fi
    
    # ML service dependencies
    if [ -d "$ML_DIR" ] && [ -f "$ML_DIR/requirements.txt" ]; then
        log "Installing ML service dependencies..."
        cd "$ML_DIR"
        if command_exists python3; then
            python3 -m pip install -r requirements.txt
        elif command_exists python; then
            python -m pip install -r requirements.txt
        else
            warn "Python not found. Skipping ML service"
        fi
        cd ../..
        log "ML service dependencies installed ✓"
    fi
}

# Build applications
build_apps() {
    header "Building Applications"
    
    # Build frontend
    if [ -d "$FRONTEND_DIR" ]; then
        log "Building frontend..."
        cd "$FRONTEND_DIR"
        if command_exists npm; then
            npm run build
        elif command_exists yarn; then
            yarn build
        fi
        cd ..
        log "Frontend built ✓"
    fi
    
    # Build backend
    if [ -d "$BACKEND_DIR" ] && [ -f "$BACKEND_DIR/pom.xml" ]; then
        log "Building backend..."
        cd "$BACKEND_DIR"
        if command_exists mvn; then
            mvn clean package -DskipTests
        fi
        cd ..
        log "Backend built ✓"
    fi
    
    # Build chatbot service
    if [ -d "$CHATBOT_DIR" ] && [ -f "$CHATBOT_DIR/pom.xml" ]; then
        log "Building chatbot service..."
        cd "$CHATBOT_DIR"
        if command_exists mvn; then
            mvn clean package -DskipTests
        fi
        cd ../..
        log "Chatbot service built ✓"
    fi
}

# Start services
start_services() {
    header "Starting Services"
    
    # Start backend first
    if [ -d "$BACKEND_DIR" ] && [ -f "$BACKEND_DIR/target"/*.jar ]; then
        log "Starting backend on port $BACKEND_PORT..."
        cd "$BACKEND_DIR"
        nohup java -jar target/*.jar --server.port=$BACKEND_PORT > backend.log 2>&1 &
        echo $! > backend.pid
        cd ..
        log "Backend started ✓"
    fi
    
    # Start ML service
    if [ -d "$ML_DIR" ] && [ -f "$ML_DIR/app.py" ]; then
        log "Starting ML service on port $ML_PORT..."
        cd "$ML_DIR"
        if command_exists python3; then
            nohup python3 app.py > ml.log 2>&1 &
        elif command_exists python; then
            nohup python app.py > ml.log 2>&1 &
        fi
        echo $! > ml.pid
        cd ../..
        log "ML service started ✓"
    fi
    
    # Start chatbot service
    if [ -d "$CHATBOT_DIR" ] && [ -f "$CHATBOT_DIR/target"/*.jar ]; then
        log "Starting chatbot service on port $CHATBOT_PORT..."
        cd "$CHATBOT_DIR"
        nohup java -jar target/*.jar --server.port=$CHATBOT_PORT > chatbot.log 2>&1 &
        echo $! > chatbot.pid
        cd ../..
        log "Chatbot service started ✓"
    fi
    
    # Start frontend (if not in production)
    if [ -d "$FRONTEND_DIR" ] && [ "$NODE_ENV" != "production" ]; then
        log "Starting frontend development server on port $FRONTEND_PORT..."
        cd "$FRONTEND_DIR"
        if command_exists npm; then
            nohup npm run dev -- --port $FRONTEND_PORT > frontend.log 2>&1 &
        elif command_exists yarn; then
            nohup yarn dev --port $FRONTEND_PORT > frontend.log 2>&1 &
        fi
        echo $! > frontend.pid
        cd ..
        log "Frontend started ✓"
    fi
}

# Stop services
stop_services() {
    header "Stopping Services"
    
    # Stop all services
    for service in frontend backend ml chatbot; do
        if [ -f "${service}.pid" ]; then
            pid=$(cat "${service}.pid")
            if kill -0 "$pid" 2>/dev/null; then
                log "Stopping $service (PID: $pid)..."
                kill "$pid"
                rm "${service}.pid"
            fi
        fi
    done
    
    log "All services stopped ✓"
}

# Check service status
check_status() {
    header "Service Status"
    
    for service in frontend backend ml chatbot; do
        if [ -f "${service}.pid" ]; then
            pid=$(cat "${service}.pid")
            if kill -0 "$pid" 2>/dev/null; then
                log "$service: Running (PID: $pid)"
            else
                warn "$service: Not running (stale PID file)"
                rm "${service}.pid"
            fi
        else
            warn "$service: Not running"
        fi
    done
}

# Setup environment
setup_env() {
    header "Environment Setup"
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        log "Creating .env file..."
        cat > .env << EOF
# MedReserve AI Environment Configuration
NODE_ENV=development
PORT=8080

# Database
DB_URL=jdbc:h2:mem:medreserve
DB_USERNAME=sa
DB_PASSWORD=

# JWT
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
JWT_EXPIRATION=86400000

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:5173

# Services
ML_SERVICE_URL=http://localhost:5000
CHATBOT_SERVICE_URL=http://localhost:8081

# Frontend
VITE_API_URL=http://localhost:8080
EOF
        log ".env file created ✓"
    else
        log ".env file already exists ✓"
    fi
}

# Main deployment function
deploy() {
    header "$APP_NAME - Universal Deployment"
    
    log "Starting deployment process..."
    
    setup_env
    install_dependencies
    build_apps
    start_services
    
    header "Deployment Complete!"
    log "Frontend: http://localhost:$FRONTEND_PORT"
    log "Backend:  http://localhost:$BACKEND_PORT"
    log "ML API:   http://localhost:$ML_PORT"
    log "Chatbot:  http://localhost:$CHATBOT_PORT"
    log ""
    log "Use './deploy.sh status' to check service status"
    log "Use './deploy.sh stop' to stop all services"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy"|"start")
        deploy
        ;;
    "stop")
        stop_services
        ;;
    "status")
        check_status
        ;;
    "restart")
        stop_services
        sleep 2
        deploy
        ;;
    "build")
        build_apps
        ;;
    "install")
        install_dependencies
        ;;
    *)
        echo "Usage: $0 {deploy|start|stop|status|restart|build|install}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment (default)"
        echo "  start    - Start all services"
        echo "  stop     - Stop all services"
        echo "  status   - Check service status"
        echo "  restart  - Restart all services"
        echo "  build    - Build applications only"
        echo "  install  - Install dependencies only"
        exit 1
        ;;
esac
