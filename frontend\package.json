{"name": "medreserve-frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.9.0", "@tanstack/react-query": "^5.83.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@vitejs/plugin-react": "^4.2.1", "axios": "^1.11.0", "date-fns": "^3.6.0", "@date-io/date-fns": "^3.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "devDependencies": {"@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "gh-pages": "^6.1.1", "jsdom": "^24.1.0", "msw": "^2.3.1", "vite": "^5.4.0", "vitest": "^1.6.0"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}