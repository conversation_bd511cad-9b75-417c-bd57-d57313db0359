#!/usr/bin/env python3
"""
Test script to verify ML service dependencies are compatible.
This script checks if all imports work without pickle5.
"""

import sys
import importlib
import subprocess

def test_import(module_name, description=""):
    """Test if a module can be imported successfully."""
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - {description}: {e}")
        return False
    except Exception as e:
        print(f"⚠️ {module_name} - {description}: {e}")
        return False

def test_pickle_functionality():
    """Test that standard pickle works for our use case."""
    try:
        import pickle
        
        # Test basic pickle functionality
        test_data = {"test": "data", "numbers": [1, 2, 3]}
        
        # Test pickle dump/load
        pickled_data = pickle.dumps(test_data)
        unpickled_data = pickle.loads(pickled_data)
        
        assert unpickled_data == test_data
        print("✅ Standard pickle functionality works correctly")
        
        # Test pickle protocol 5 (available in Python 3.8+)
        if sys.version_info >= (3, 8):
            pickled_v5 = pickle.dumps(test_data, protocol=5)
            unpickled_v5 = pickle.loads(pickled_v5)
            assert unpickled_v5 == test_data
            print("✅ Pickle protocol 5 works correctly (no pickle5 package needed)")
        
        return True
    except Exception as e:
        print(f"❌ Pickle functionality test failed: {e}")
        return False

def check_python_version():
    """Check Python version compatibility."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 8):
        print("✅ Python version supports pickle protocol 5 natively")
        return True
    else:
        print("⚠️ Python version < 3.8, pickle5 package would be needed")
        return False

def main():
    """Run all compatibility tests."""
    print("🧪 ML Service Dependency Compatibility Test")
    print("=" * 50)
    
    # Check Python version
    python_ok = check_python_version()
    print()
    
    # Test core dependencies that should be available
    core_tests = [
        ("os", "Operating system interface"),
        ("sys", "System-specific parameters"),
        ("json", "JSON encoder/decoder"),
        ("pickle", "Python object serialization"),
        ("typing", "Type hints support"),
    ]
    
    print("🔧 Testing Core Dependencies:")
    core_results = []
    for module, desc in core_tests:
        result = test_import(module, desc)
        core_results.append(result)
    
    print()
    
    # Test pickle functionality specifically
    print("🥒 Testing Pickle Functionality:")
    pickle_ok = test_pickle_functionality()
    print()
    
    # Test optional ML dependencies (may not be installed locally)
    print("🤖 Testing ML Dependencies (may not be installed locally):")
    ml_tests = [
        ("numpy", "Numerical computing"),
        ("pandas", "Data manipulation"),
        ("sklearn", "Machine learning library"),
        ("joblib", "Lightweight pipelining"),
        ("fastapi", "Web framework"),
        ("uvicorn", "ASGI server"),
        ("pydantic", "Data validation"),
    ]
    
    ml_results = []
    for module, desc in ml_tests:
        result = test_import(module, desc)
        ml_results.append(result)
    
    print()
    
    # Summary
    print("📊 Test Summary:")
    print("=" * 30)
    
    core_passed = sum(core_results)
    ml_passed = sum(ml_results)
    
    print(f"Core dependencies: {core_passed}/{len(core_results)} passed")
    print(f"ML dependencies: {ml_passed}/{len(ml_results)} passed")
    print(f"Pickle functionality: {'✅ PASS' if pickle_ok else '❌ FAIL'}")
    print(f"Python version: {'✅ COMPATIBLE' if python_ok else '⚠️ OLD'}")
    
    # Overall assessment
    if core_passed == len(core_results) and pickle_ok and python_ok:
        print("\n🎉 All critical tests passed!")
        print("✅ ML service should work without pickle5 dependency")
        return True
    else:
        print("\n⚠️ Some tests failed")
        if not pickle_ok:
            print("❌ Pickle functionality issues detected")
        if not python_ok:
            print("⚠️ Python version may need pickle5 package")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
