package com.medreserve.security;

import com.medreserve.entity.Role;
import com.medreserve.entity.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

class JwtUtilsTest {
    
    private JwtUtils jwtUtils;
    private User testUser;
    private Authentication authentication;
    
    @BeforeEach
    void setUp() {
        jwtUtils = new JwtUtils();
        ReflectionTestUtils.setField(jwtUtils, "jwtSecret", "mySecretKey123456789012345678901234567890");
        ReflectionTestUtils.setField(jwtUtils, "jwtExpirationMs", 86400000);
        ReflectionTestUtils.setField(jwtUtils, "jwtRefreshExpirationMs", 604800000);
        
        // Create test user
        Role role = new Role();
        role.setName(Role.RoleName.PATIENT);
        
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(role);
        testUser.setIsActive(true);
        testUser.setEmailVerified(true);
        
        authentication = new UsernamePasswordAuthenticationToken(testUser, null, testUser.getAuthorities());
    }
    
    @Test
    void testGenerateJwtToken() {
        String token = jwtUtils.generateJwtToken(authentication);
        
        assertNotNull(token);
        assertFalse(token.isEmpty());
        assertTrue(token.split("\\.").length == 3); // JWT has 3 parts
    }
    
    @Test
    void testGenerateRefreshToken() {
        String refreshToken = jwtUtils.generateRefreshToken(authentication);
        
        assertNotNull(refreshToken);
        assertFalse(refreshToken.isEmpty());
        assertTrue(refreshToken.split("\\.").length == 3);
    }
    
    @Test
    void testGetUserNameFromJwtToken() {
        String token = jwtUtils.generateJwtToken(authentication);
        String username = jwtUtils.getUserNameFromJwtToken(token);
        
        assertEquals("<EMAIL>", username);
    }
    
    @Test
    void testValidateJwtToken() {
        String token = jwtUtils.generateJwtToken(authentication);
        
        assertTrue(jwtUtils.validateJwtToken(token));
    }
    
    @Test
    void testValidateInvalidJwtToken() {
        String invalidToken = "invalid.token.here";
        
        assertFalse(jwtUtils.validateJwtToken(invalidToken));
    }
    
    @Test
    void testIsTokenExpired() {
        String token = jwtUtils.generateJwtToken(authentication);
        
        assertFalse(jwtUtils.isTokenExpired(token));
    }
    
    @Test
    void testGetExpirationDateFromToken() {
        String token = jwtUtils.generateJwtToken(authentication);
        
        assertNotNull(jwtUtils.getExpirationDateFromToken(token));
        assertTrue(jwtUtils.getExpirationDateFromToken(token).getTime() > System.currentTimeMillis());
    }
}
