const axios = require('axios');

const BACKEND_URL = 'https://medreserve-ai-backend.onrender.com';

async function testAuthenticationSystem() {
  console.log('🔐 Testing MedReserve Authentication System');
  console.log('=' .repeat(60));

  const testResults = {
    signup: { status: 'unknown', details: null },
    loginUnverified: { status: 'unknown', details: null },
    masterAdminLogin: { status: 'unknown', details: null },
    tokenValidation: { status: 'unknown', details: null },
    protectedEndpoint: { status: 'unknown', details: null }
  };

  // Test data with unique identifiers
  const timestamp = Date.now();
  const testUser = {
    firstName: 'Test',
    lastName: 'User',
    email: `test.user.${timestamp}@example.com`,
    password: 'TestPassword123!',
    role: 'PATIENT',
    phoneNumber: `+123456${timestamp.toString().slice(-4)}`, // Unique phone number
    dateOfBirth: '1990-01-01'
  };

  console.log(`Using test email: ${testUser.email}`);
  console.log(`Using test phone: ${testUser.phoneNumber}`);

  try {
    // 1. Test Signup
    console.log('\n1️⃣ Testing User Signup...');
    try {
      const signupResponse = await axios.post(`${BACKEND_URL}/auth/signup`, testUser, {
        timeout: 15000,
        validateStatus: () => true
      });
      
      testResults.signup.status = signupResponse.status < 400 ? 'success' : 'failed';
      testResults.signup.details = {
        status: signupResponse.status,
        message: signupResponse.data?.message || 'No message',
        hasToken: !!signupResponse.data?.token
      };
      
      console.log(`Status: ${signupResponse.status}`);
      console.log(`Response: ${JSON.stringify(signupResponse.data, null, 2)}`);
      
      if (signupResponse.status === 201 || signupResponse.status === 200) {
        console.log('✅ Signup successful');
      } else {
        console.log('❌ Signup failed');
      }
      
    } catch (signupError) {
      testResults.signup.status = 'error';
      testResults.signup.details = { error: signupError.message };
      console.log(`❌ Signup error: ${signupError.message}`);
    }

    // 2. Test Login with Unverified User (Expected to Fail)
    console.log('\n2️⃣ Testing Login with Unverified User (Expected to Fail)...');

    try {
      const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      }, {
        timeout: 15000,
        validateStatus: () => true
      });

      testResults.loginUnverified.status = loginResponse.status === 400 ? 'expected_failure' : 'unexpected';
      testResults.loginUnverified.details = {
        status: loginResponse.status,
        message: loginResponse.data?.message || 'No message',
        hasToken: !!loginResponse.data?.token
      };

      console.log(`Status: ${loginResponse.status}`);
      console.log(`Response: ${JSON.stringify(loginResponse.data, null, 2)}`);

      if (loginResponse.status === 400) {
        console.log('✅ Login correctly failed for unverified user (expected behavior)');
      } else {
        console.log('⚠️ Unexpected response for unverified user login');
      }

    } catch (loginError) {
      testResults.loginUnverified.status = 'error';
      testResults.loginUnverified.details = { error: loginError.message };
      console.log(`❌ Login error: ${loginError.message}`);
    }

    // 3. Test Login with Master Admin (Should Work)
    console.log('\n3️⃣ Testing Login with Master Admin...');
    let authToken = null;

    try {
      const adminLoginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      }, {
        timeout: 15000,
        validateStatus: () => true
      });

      testResults.masterAdminLogin.status = adminLoginResponse.status === 200 ? 'success' : 'failed';
      testResults.masterAdminLogin.details = {
        status: adminLoginResponse.status,
        message: adminLoginResponse.data?.message || 'No message',
        hasToken: !!(adminLoginResponse.data?.token || adminLoginResponse.data?.accessToken),
        tokenType: adminLoginResponse.data?.type || 'unknown'
      };

      console.log(`Status: ${adminLoginResponse.status}`);
      console.log(`Response: ${JSON.stringify(adminLoginResponse.data, null, 2)}`);

      if (adminLoginResponse.status === 200 && (adminLoginResponse.data?.token || adminLoginResponse.data?.accessToken)) {
        authToken = adminLoginResponse.data.token || adminLoginResponse.data.accessToken;
        console.log('✅ Master Admin login successful with token');
      } else {
        console.log('❌ Master Admin login failed or no token received');
      }

    } catch (adminLoginError) {
      testResults.masterAdminLogin.status = 'error';
      testResults.masterAdminLogin.details = { error: adminLoginError.message };
      console.log(`❌ Master Admin login error: ${adminLoginError.message}`);
    }

    // 4. Test Token Validation
    console.log('\n4️⃣ Testing Token Validation...');
    if (authToken) {
      try {
        const meResponse = await axios.get(`${BACKEND_URL}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000,
          validateStatus: () => true
        });
        
        testResults.tokenValidation.status = meResponse.status === 200 ? 'success' : 'failed';
        testResults.tokenValidation.details = {
          status: meResponse.status,
          userInfo: meResponse.data || null
        };
        
        console.log(`Status: ${meResponse.status}`);
        console.log(`User Info: ${JSON.stringify(meResponse.data, null, 2)}`);
        
        if (meResponse.status === 200) {
          console.log('✅ Token validation successful');
        } else {
          console.log('❌ Token validation failed');
        }
        
      } catch (tokenError) {
        testResults.tokenValidation.status = 'error';
        testResults.tokenValidation.details = { error: tokenError.message };
        console.log(`❌ Token validation error: ${tokenError.message}`);
      }
    } else {
      testResults.tokenValidation.status = 'skipped';
      testResults.tokenValidation.details = { reason: 'No token available from login' };
      console.log('⏭️ Skipped - no token available');
    }

    // 5. Test Protected Endpoint
    console.log('\n5️⃣ Testing Protected Endpoint Access...');
    if (authToken) {
      try {
        const doctorsResponse = await axios.get(`${BACKEND_URL}/doctors/specialties`, {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000,
          validateStatus: () => true
        });
        
        testResults.protectedEndpoint.status = doctorsResponse.status === 200 ? 'success' : 'failed';
        testResults.protectedEndpoint.details = {
          status: doctorsResponse.status,
          dataReceived: !!doctorsResponse.data,
          dataLength: Array.isArray(doctorsResponse.data) ? doctorsResponse.data.length : 'not array'
        };
        
        console.log(`Status: ${doctorsResponse.status}`);
        console.log(`Data received: ${!!doctorsResponse.data}`);
        
        if (doctorsResponse.status === 200) {
          console.log('✅ Protected endpoint access successful');
        } else {
          console.log('❌ Protected endpoint access failed');
        }
        
      } catch (endpointError) {
        testResults.protectedEndpoint.status = 'error';
        testResults.protectedEndpoint.details = { error: endpointError.message };
        console.log(`❌ Protected endpoint error: ${endpointError.message}`);
      }
    } else {
      testResults.protectedEndpoint.status = 'skipped';
      testResults.protectedEndpoint.details = { reason: 'No token available' };
      console.log('⏭️ Skipped - no token available');
    }

  } catch (generalError) {
    console.log(`❌ General error: ${generalError.message}`);
  }

  // Summary
  console.log('\n📊 AUTHENTICATION TEST SUMMARY');
  console.log('=' .repeat(60));
  
  Object.entries(testResults).forEach(([test, result]) => {
    const statusIcon = result.status === 'success' ? '✅' : 
                      result.status === 'failed' ? '❌' : 
                      result.status === 'error' ? '🔥' : '⏭️';
    console.log(`${statusIcon} ${test.toUpperCase()}: ${result.status}`);
    
    if (result.details && result.details.error) {
      console.log(`   └─ Error: ${result.details.error}`);
    } else if (result.details && result.details.status) {
      console.log(`   └─ HTTP Status: ${result.details.status}`);
    }
  });

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (testResults.signup.status === 'failed') {
    console.log('⚠️ Signup issues detected - check validation rules and database constraints');
  }

  if (testResults.loginUnverified.status !== 'expected_failure') {
    console.log('⚠️ Unverified user login behavior unexpected - check email verification logic');
  }

  if (testResults.masterAdminLogin.status === 'failed') {
    console.log('⚠️ Master Admin login issues detected - check default admin setup');
  }
  
  if (testResults.tokenValidation.status === 'failed') {
    console.log('⚠️ Token validation issues - check JWT secret and token format');
  }
  
  if (testResults.protectedEndpoint.status === 'failed') {
    console.log('⚠️ Authorization issues - check role-based access control');
  }

  const successCount = Object.values(testResults).filter(r => r.status === 'success').length;
  const totalTests = Object.values(testResults).filter(r => r.status !== 'skipped').length;
  
  console.log(`\n🎯 Overall Success Rate: ${successCount}/${totalTests} tests passed`);
  
  if (successCount === totalTests) {
    console.log('🎉 All authentication tests passed! System is working correctly.');
  } else {
    console.log('⚠️ Some authentication tests failed. Review the issues above.');
  }
}

// Run the authentication test
testAuthenticationSystem().catch(console.error);
