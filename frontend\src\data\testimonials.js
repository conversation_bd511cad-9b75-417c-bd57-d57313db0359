export const testimonials = [
  {
    id: 1,
    patient<PERSON>ame: "<PERSON>",
    doctor<PERSON>ame: "Dr. <PERSON><PERSON>",
    specialty: "Cardiology",
    rating: 5,
    comment: "Dr. <PERSON> is an exceptional cardiologist. His thorough examination and clear explanation of my condition gave me great confidence. The treatment plan he provided has significantly improved my heart health.",
    date: "2024-07-15",
    verified: true
  },
  {
    id: 2,
    patient<PERSON>ame: "<PERSON>",
    doctor<PERSON>ame: "Dr. <PERSON><PERSON>",
    specialty: "Cardiology",
    rating: 5,
    comment: "Outstanding care and professionalism. Dr<PERSON> took the time to explain every aspect of my cardiac procedure. I felt completely at ease throughout the entire process.",
    date: "2024-07-10",
    verified: true
  },
  {
    id: 3,
    patientName: "<PERSON>",
    doctor<PERSON>ame: "Dr. <PERSON>",
    specialty: "Neurology",
    rating: 4,
    comment: "Dr<PERSON> <PERSON>'s expertise in neurology is remarkable. He diagnosed my condition quickly and provided effective treatment. Very satisfied with the care received.",
    date: "2024-07-08",
    verified: true
  },
  {
    id: 4,
    patientName: "<PERSON>",
    doctor<PERSON><PERSON>: "Dr. <PERSON>",
    specialty: "Dermatology",
    rating: 5,
    comment: "Excellent dermatologist! Dr<PERSON> resolved my skin condition that I had been struggling with for months. Her approach is both professional and caring.",
    date: "2024-07-05",
    verified: true
  },
  {
    id: 5,
    patient<PERSON>ame: "<PERSON>",
    doctor<PERSON>ame: "Dr. <PERSON> <PERSON>",
    specialty: "Orthopedics",
    rating: 5,
    comment: "Dr. <PERSON> performed my knee surgery with incredible skill. The recovery was smooth and I'm back to my normal activities. Highly recommend!",
    date: "2024-07-02",
    verified: true
  },
  {
    id: 6,
    patient<PERSON>ame: "<PERSON> <PERSON>",
    doctor<PERSON>ame: "Dr. <PERSON> <PERSON>",
    specialty: "Pediatrics",
    rating: 5,
    comment: "Dr. <PERSON> is wonderful with children. My daughter feels comfortable during visits and Dr. Garcia always takes time to address all our concerns.",
    date: "2024-06-28",
    verified: true
  },
  {
    id: 7,
    patientName: "Amanda Martinez",
    doctorName: "Dr. Elena Rodriguez",
    specialty: "Psychiatry",
    rating: 4,
    comment: "Dr. Rodriguez has been instrumental in my mental health journey. Her compassionate approach and effective treatment strategies have made a significant difference.",
    date: "2024-06-25",
    verified: true
  },
  {
    id: 8,
    patientName: "Matthew Anderson",
    doctorName: "Dr. Sunita Rao",
    specialty: "Oncology",
    rating: 5,
    comment: "Dr. Rao guided me through my cancer treatment with exceptional care and expertise. Her positive attitude and thorough explanations helped me stay strong.",
    date: "2024-06-20",
    verified: true
  },
  {
    id: 9,
    patientName: "Ashley Taylor",
    doctorName: "Dr. Sophia Williams",
    specialty: "Gynecology",
    rating: 5,
    comment: "Dr. Williams provides excellent gynecological care. She's thorough, professional, and makes you feel comfortable discussing sensitive health matters.",
    date: "2024-06-18",
    verified: true
  },
  {
    id: 10,
    patientName: "Daniel Thomas",
    doctorName: "Dr. Alexander Kumar",
    specialty: "Ophthalmology",
    rating: 4,
    comment: "Great experience with Dr. Kumar for my eye surgery. The procedure was successful and my vision has improved significantly. Professional and skilled doctor.",
    date: "2024-06-15",
    verified: true
  },
  {
    id: 11,
    patientName: "Stephanie Jackson",
    doctorName: "Dr. Ahmed Hassan",
    specialty: "ENT",
    rating: 5,
    comment: "Dr. Hassan resolved my chronic sinus issues effectively. His expertise in ENT is evident and the treatment was successful. Very satisfied with the results.",
    date: "2024-06-12",
    verified: true
  },
  {
    id: 12,
    patientName: "Ryan White",
    doctorName: "Dr. Rajesh Patel",
    specialty: "Endocrinology",
    rating: 5,
    comment: "Dr. Patel has been managing my diabetes excellently. His comprehensive approach and regular monitoring have helped me maintain stable blood sugar levels.",
    date: "2024-06-10",
    verified: true
  },
  {
    id: 13,
    patientName: "Nicole Harris",
    doctorName: "Dr. Amit Singh",
    specialty: "Cardiology",
    rating: 4,
    comment: "Professional and knowledgeable cardiologist. Dr. Singh provided clear guidance on managing my heart condition and the treatment has been effective.",
    date: "2024-06-08",
    verified: true
  },
  {
    id: 14,
    patientName: "Kevin Martin",
    doctorName: "Dr. Emily Davis",
    specialty: "Neurology",
    rating: 5,
    comment: "Dr. Davis is an outstanding neurologist. Her detailed examination and accurate diagnosis led to effective treatment for my neurological condition.",
    date: "2024-06-05",
    verified: true
  },
  {
    id: 15,
    patientName: "Rachel Thompson",
    doctorName: "Dr. Kevin Taylor",
    specialty: "Dermatology",
    rating: 4,
    comment: "Good experience with Dr. Taylor. He addressed my skin concerns professionally and the prescribed treatment worked well. Satisfied with the care.",
    date: "2024-06-02",
    verified: true
  },
  {
    id: 16,
    patientName: "Brandon Moore",
    doctorName: "Dr. Amanda Clark",
    specialty: "Orthopedics",
    rating: 5,
    comment: "Excellent orthopedic surgeon! Dr. Clark's expertise helped me recover from my sports injury completely. The rehabilitation plan was perfect.",
    date: "2024-05-30",
    verified: true
  },
  {
    id: 17,
    patientName: "Lauren Clark",
    doctorName: "Dr. Thomas Hall",
    specialty: "Pediatrics",
    rating: 5,
    comment: "Dr. Hall is fantastic with kids. My son always looks forward to his appointments. Professional, caring, and very knowledgeable pediatrician.",
    date: "2024-05-28",
    verified: true
  },
  {
    id: 18,
    patientName: "Justin Rodriguez",
    doctorName: "Dr. Marcus Thompson",
    specialty: "Psychiatry",
    rating: 4,
    comment: "Dr. Thompson has been very helpful in my therapy sessions. His approach is professional and he provides practical strategies for managing stress.",
    date: "2024-05-25",
    verified: true
  },
  {
    id: 19,
    patientName: "Megan Lewis",
    doctorName: "Dr. Richard Chen",
    specialty: "Oncology",
    rating: 5,
    comment: "Dr. Chen is an exceptional oncologist. His expertise and compassionate care during my treatment journey were invaluable. Highly recommended.",
    date: "2024-05-22",
    verified: true
  },
  {
    id: 20,
    patientName: "Tyler Lee",
    doctorName: "Dr. Maria Gonzalez",
    specialty: "Gynecology",
    rating: 5,
    comment: "Dr. Gonzalez provides excellent women's healthcare. She's thorough, professional, and makes you feel comfortable. Very satisfied with the care.",
    date: "2024-05-20",
    verified: true
  }
];

export const getTestimonialsByDoctor = (doctorName) => {
  return testimonials.filter(t => t.doctorName === doctorName);
};

export const getTestimonialsBySpecialty = (specialty) => {
  return testimonials.filter(t => t.specialty === specialty);
};

export const getAverageRating = (doctorName, specialty) => {
  let filteredTestimonials = testimonials;
  
  if (doctorName) {
    filteredTestimonials = filteredTestimonials.filter(t => t.doctorName === doctorName);
  }
  
  if (specialty) {
    filteredTestimonials = filteredTestimonials.filter(t => t.specialty === specialty);
  }
  
  if (filteredTestimonials.length === 0) return 0;
  
  const sum = filteredTestimonials.reduce((acc, t) => acc + t.rating, 0);
  return Math.round((sum / filteredTestimonials.length) * 10) / 10;
};

export const getTotalReviews = (doctorName, specialty) => {
  let filteredTestimonials = testimonials;
  
  if (doctorName) {
    filteredTestimonials = filteredTestimonials.filter(t => t.doctorName === doctorName);
  }
  
  if (specialty) {
    filteredTestimonials = filteredTestimonials.filter(t => t.specialty === specialty);
  }
  
  return filteredTestimonials.length;
};
