#!/usr/bin/env node

/**
 * MedReserve AI - Universal Startup Script
 * Works on Windows, macOS, and Linux without platform-specific configurations
 * 
 * Usage:
 *   node start-medreserve.js
 *   npm start (if added to package.json scripts)
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuration
const config = {
  frontend: {
    dir: 'frontend',
    command: 'npm',
    args: ['run', 'dev'],
    port: 3001,
    name: 'Frontend (React + Vite)'
  },
  backend: {
    dir: 'backend',
    command: 'mvn',
    args: ['spring-boot:run', '-Dspring-boot.run.arguments=--spring.profiles.active=dev'],
    port: 8080,
    name: 'Backend (Spring Boot)'
  }
};

// Utility functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(`  ${title}`, 'cyan');
  console.log('='.repeat(60));
}

function checkDirectory(dir) {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    log(`❌ Directory not found: ${dir}`, 'red');
    return false;
  }
  return true;
}

function checkPackageJson(dir) {
  const packagePath = path.join(process.cwd(), dir, 'package.json');
  return fs.existsSync(packagePath);
}

function checkPomXml(dir) {
  const pomPath = path.join(process.cwd(), dir, 'pom.xml');
  return fs.existsSync(pomPath);
}

function isPortInUse(port) {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => resolve(false));
      server.close();
    });
    
    server.on('error', () => resolve(true));
  });
}

function runCommand(command, args, cwd, name) {
  return new Promise((resolve, reject) => {
    log(`🚀 Starting ${name}...`, 'yellow');
    
    const isWindows = os.platform() === 'win32';
    const cmd = isWindows && command === 'npm' ? 'npm.cmd' : command;
    const finalArgs = isWindows && command === 'mvn' ? ['spring-boot:run', '"-Dspring-boot.run.arguments=--spring.profiles.active=dev"'] : args;
    
    const child = spawn(cmd, finalArgs, {
      cwd: path.join(process.cwd(), cwd),
      stdio: 'pipe',
      shell: isWindows
    });

    let started = false;
    
    child.stdout.on('data', (data) => {
      const output = data.toString();
      
      // Check for startup indicators
      if (!started) {
        if (
          (name.includes('Frontend') && (output.includes('Local:') || output.includes('ready in'))) ||
          (name.includes('Backend') && (output.includes('Tomcat started') || output.includes('Started MedReserveApplication')))
        ) {
          started = true;
          log(`✅ ${name} started successfully!`, 'green');
          resolve(child);
        }
      }
      
      // Log important messages
      if (output.includes('ERROR') || output.includes('error')) {
        log(`❌ ${name}: ${output.trim()}`, 'red');
      } else if (output.includes('WARN') || output.includes('warn')) {
        log(`⚠️  ${name}: ${output.trim()}`, 'yellow');
      }
    });

    child.stderr.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('Download') && !error.includes('Progress')) {
        log(`❌ ${name} Error: ${error.trim()}`, 'red');
      }
    });

    child.on('error', (error) => {
      log(`❌ Failed to start ${name}: ${error.message}`, 'red');
      reject(error);
    });

    child.on('exit', (code) => {
      if (code !== 0 && !started) {
        log(`❌ ${name} exited with code ${code}`, 'red');
        reject(new Error(`Process exited with code ${code}`));
      }
    });

    // Timeout after 2 minutes if not started
    setTimeout(() => {
      if (!started) {
        log(`⏰ ${name} startup timeout`, 'yellow');
        resolve(child);
      }
    }, 120000);
  });
}

async function installDependencies() {
  logSection('Installing Dependencies');
  
  // Install frontend dependencies
  if (checkPackageJson('frontend')) {
    log('📦 Installing frontend dependencies...', 'blue');
    await new Promise((resolve, reject) => {
      const isWindows = os.platform() === 'win32';
      const npm = isWindows ? 'npm.cmd' : 'npm';
      
      const child = spawn(npm, ['install'], {
        cwd: path.join(process.cwd(), 'frontend'),
        stdio: 'pipe',
        shell: isWindows
      });
      
      child.on('close', (code) => {
        if (code === 0) {
          log('✅ Frontend dependencies installed', 'green');
          resolve();
        } else {
          log('❌ Failed to install frontend dependencies', 'red');
          reject(new Error(`npm install failed with code ${code}`));
        }
      });
    });
  }
  
  log('✅ Dependencies ready', 'green');
}

async function checkPorts() {
  logSection('Checking Ports');
  
  const frontendPortInUse = await isPortInUse(config.frontend.port);
  const backendPortInUse = await isPortInUse(config.backend.port);
  
  if (frontendPortInUse) {
    log(`⚠️  Port ${config.frontend.port} is in use (Frontend will use next available)`, 'yellow');
  } else {
    log(`✅ Port ${config.frontend.port} available for Frontend`, 'green');
  }
  
  if (backendPortInUse) {
    log(`⚠️  Port ${config.backend.port} is in use (Backend may fail to start)`, 'yellow');
  } else {
    log(`✅ Port ${config.backend.port} available for Backend`, 'green');
  }
}

async function startServices() {
  logSection('Starting Services');
  
  const processes = [];
  
  try {
    // Start backend first
    if (checkDirectory(config.backend.dir) && checkPomXml(config.backend.dir)) {
      const backendProcess = await runCommand(
        config.backend.command,
        config.backend.args,
        config.backend.dir,
        config.backend.name
      );
      processes.push(backendProcess);
      
      // Wait a bit for backend to stabilize
      await new Promise(resolve => setTimeout(resolve, 5000));
    } else {
      log('❌ Backend directory or pom.xml not found', 'red');
    }
    
    // Start frontend
    if (checkDirectory(config.frontend.dir) && checkPackageJson(config.frontend.dir)) {
      const frontendProcess = await runCommand(
        config.frontend.command,
        config.frontend.args,
        config.frontend.dir,
        config.frontend.name
      );
      processes.push(frontendProcess);
    } else {
      log('❌ Frontend directory or package.json not found', 'red');
    }
    
    return processes;
  } catch (error) {
    log(`❌ Error starting services: ${error.message}`, 'red');
    return processes;
  }
}

function showStatus() {
  logSection('MedReserve AI - Healthcare Management Platform');
  
  log('🏥 Application Status:', 'bright');
  log(`   Frontend: http://localhost:${config.frontend.port}`, 'cyan');
  log(`   Backend:  http://localhost:${config.backend.port}`, 'cyan');
  log('', 'reset');
  log('📋 Available Features:', 'bright');
  log('   ✅ Dashboard with health metrics', 'green');
  log('   ✅ Doctor listings and profiles', 'green');
  log('   ✅ Appointment booking system', 'green');
  log('   ✅ AI Chatbot assistance', 'green');
  log('   ✅ Appointment management', 'green');
  log('   ✅ Medical reports and medicines', 'green');
  log('', 'reset');
  log('🔧 Controls:', 'bright');
  log('   Press Ctrl+C to stop all services', 'yellow');
}

async function main() {
  console.clear();
  
  logSection('MedReserve AI - Universal Startup');
  log('🏥 Healthcare Management Platform', 'bright');
  log('🌍 Cross-platform startup script', 'blue');
  
  try {
    // Check prerequisites
    if (!checkDirectory('frontend') || !checkDirectory('backend')) {
      log('❌ Required directories not found. Please run from project root.', 'red');
      process.exit(1);
    }
    
    // Install dependencies
    await installDependencies();
    
    // Check ports
    await checkPorts();
    
    // Start services
    const processes = await startServices();
    
    // Show status
    showStatus();
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      log('\n🛑 Shutting down services...', 'yellow');
      processes.forEach(proc => {
        if (proc && !proc.killed) {
          proc.kill('SIGTERM');
        }
      });
      
      setTimeout(() => {
        log('✅ All services stopped', 'green');
        process.exit(0);
      }, 2000);
    });
    
    // Keep the process alive
    process.stdin.resume();
    
  } catch (error) {
    log(`❌ Startup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the application
if (require.main === module) {
  main();
}

module.exports = { main, config };
