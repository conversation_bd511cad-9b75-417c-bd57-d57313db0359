const axios = require('axios');

// Deployment URLs
const deployments = {
  frontend: 'https://rishith2903.github.io/MedReserve-AI/',
  backend: 'https://medreserve-ai-backend.onrender.com',
  ml: 'https://medreserve-ml.onrender.com',
  chatbot: 'https://medreserve-chatbot.onrender.com'
};

// Health check endpoints
const healthEndpoints = {
  backend: '/actuator/health',
  ml: '/health',
  chatbot: '/health'
};

// Performance thresholds (milliseconds)
const performanceThresholds = {
  excellent: 500,
  good: 1000,
  acceptable: 2000,
  poor: 5000
};

// Test deployment health
async function testDeploymentHealth() {
  console.log('🏥 MedReserve Deployment Health Check');
  console.log('=====================================');
  
  const results = {
    services: {},
    overall: 'healthy'
  };
  
  // Test each service
  for (const [service, url] of Object.entries(deployments)) {
    console.log(`\n🔍 Testing ${service.toUpperCase()}`);
    console.log(`URL: ${url}`);
    console.log('─'.repeat(50));
    
    const serviceResult = {
      status: 'unknown',
      responseTime: 0,
      statusCode: 0,
      error: null
    };
    
    try {
      const startTime = Date.now();
      
      if (service === 'frontend') {
        // Test frontend accessibility
        const response = await axios.get(url, { 
          timeout: 10000,
          headers: {
            'User-Agent': 'MedReserve-Health-Check'
          }
        });
        serviceResult.responseTime = Date.now() - startTime;
        serviceResult.statusCode = response.status;
        
        if (response.status === 200) {
          serviceResult.status = 'healthy';
          console.log('✅ Frontend accessible');
          console.log(`   Status: ${response.status}`);
          console.log(`   Response time: ${serviceResult.responseTime}ms`);
          
          // Check if it's the correct app
          if (response.data.includes('MedReserve') || response.data.includes('medreserve')) {
            console.log('✅ Correct application detected');
          } else {
            console.log('⚠️  Application content verification needed');
          }
        }
      } else {
        // Test backend services
        const healthEndpoint = healthEndpoints[service];
        const response = await axios.get(`${url}${healthEndpoint}`, { 
          timeout: 10000 
        });
        serviceResult.responseTime = Date.now() - startTime;
        serviceResult.statusCode = response.status;
        
        if (response.status === 200) {
          const healthData = response.data;
          serviceResult.status = healthData.status === 'UP' || healthData.status === 'healthy' ? 'healthy' : 'degraded';
          
          console.log(`✅ ${service} service healthy`);
          console.log(`   Status: ${healthData.status || 'OK'}`);
          console.log(`   Response time: ${serviceResult.responseTime}ms`);
          
          // Additional health info
          if (healthData.components) {
            console.log('   Components:');
            Object.entries(healthData.components).forEach(([comp, status]) => {
              console.log(`     ${comp}: ${status.status || status}`);
            });
          }
        }
      }
      
      // Performance assessment
      const perfLevel = getPerformanceLevel(serviceResult.responseTime);
      console.log(`   Performance: ${perfLevel.emoji} ${perfLevel.level} (${serviceResult.responseTime}ms)`);
      
    } catch (error) {
      serviceResult.error = error.message;
      serviceResult.statusCode = error.response?.status || 0;
      serviceResult.status = 'unhealthy';
      
      console.log(`❌ ${service} service failed`);
      console.log(`   Error: ${error.message}`);
      if (error.response?.status) {
        console.log(`   Status Code: ${error.response.status}`);
      }
      
      results.overall = 'degraded';
    }
    
    results.services[service] = serviceResult;
  }
  
  return results;
}

// Get performance level
function getPerformanceLevel(responseTime) {
  if (responseTime <= performanceThresholds.excellent) {
    return { level: 'Excellent', emoji: '🚀' };
  } else if (responseTime <= performanceThresholds.good) {
    return { level: 'Good', emoji: '✅' };
  } else if (responseTime <= performanceThresholds.acceptable) {
    return { level: 'Acceptable', emoji: '⚠️' };
  } else {
    return { level: 'Poor', emoji: '🐌' };
  }
}

// Test cross-service communication
async function testServiceIntegration() {
  console.log('\n🔗 Testing Service Integration');
  console.log('==============================');
  
  try {
    // Test backend to ML service
    console.log('\n📡 Backend → ML Service');
    const mlResponse = await axios.post(`${deployments.backend}/ml/predict-specialty`, {
      symptoms: 'fever, headache'
    }, {
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (mlResponse.status === 200 || mlResponse.status === 403) {
      console.log('✅ Backend can reach ML service');
    }
  } catch (error) {
    console.log(`❌ Backend → ML integration failed: ${error.response?.status || error.message}`);
  }
  
  try {
    // Test backend to chatbot service
    console.log('\n💬 Backend → Chatbot Service');
    const chatResponse = await axios.post(`${deployments.backend}/ai/chatbot`, {
      message: 'hello'
    }, {
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (chatResponse.status === 200 || chatResponse.status === 403) {
      console.log('✅ Backend can reach Chatbot service');
    }
  } catch (error) {
    console.log(`❌ Backend → Chatbot integration failed: ${error.response?.status || error.message}`);
  }
}

// Test database connectivity
async function testDatabaseHealth() {
  console.log('\n🗃️  Database Health Check');
  console.log('=========================');
  
  try {
    // Test database through backend health endpoint
    const healthResponse = await axios.get(`${deployments.backend}/actuator/health`, {
      timeout: 10000
    });
    
    if (healthResponse.data.components?.db) {
      const dbStatus = healthResponse.data.components.db.status;
      console.log(`✅ Database status: ${dbStatus}`);
      
      if (healthResponse.data.components.db.details) {
        console.log('   Database details:');
        Object.entries(healthResponse.data.components.db.details).forEach(([key, value]) => {
          console.log(`     ${key}: ${value}`);
        });
      }
    } else {
      console.log('⚠️  Database health info not available');
    }
  } catch (error) {
    console.log(`❌ Database health check failed: ${error.message}`);
  }
}

// Generate monitoring recommendations
function generateMonitoringRecommendations(results) {
  console.log('\n📊 Monitoring Recommendations');
  console.log('=============================');
  
  console.log('\n🔧 Suggested Monitoring Tools:');
  console.log('1. **Uptime Monitoring**: UptimeRobot, Pingdom, or StatusCake');
  console.log('2. **Performance Monitoring**: New Relic, DataDog, or Grafana');
  console.log('3. **Log Aggregation**: ELK Stack, Splunk, or Papertrail');
  console.log('4. **Error Tracking**: Sentry, Rollbar, or Bugsnag');
  
  console.log('\n📈 Key Metrics to Monitor:');
  console.log('- Response time (target: <1s for API, <3s for frontend)');
  console.log('- Error rate (target: <1%)');
  console.log('- Uptime (target: 99.9%)');
  console.log('- Database connection pool usage');
  console.log('- Memory and CPU usage');
  
  console.log('\n🚨 Alert Thresholds:');
  console.log('- Response time > 5s');
  console.log('- Error rate > 5%');
  console.log('- Service downtime > 1 minute');
  console.log('- Database connection failures');
  
  console.log('\n📋 Health Check Endpoints:');
  Object.entries(deployments).forEach(([service, url]) => {
    if (service !== 'frontend') {
      const endpoint = healthEndpoints[service];
      console.log(`- ${service}: ${url}${endpoint}`);
    }
  });
}

// Main health check function
async function runHealthCheck() {
  const results = await testDeploymentHealth();
  await testServiceIntegration();
  await testDatabaseHealth();
  generateMonitoringRecommendations(results);
  
  console.log('\n🎯 Health Check Summary');
  console.log('=======================');
  
  const healthyServices = Object.values(results.services).filter(s => s.status === 'healthy').length;
  const totalServices = Object.keys(results.services).length;
  
  console.log(`✅ Healthy services: ${healthyServices}/${totalServices}`);
  console.log(`📊 Overall status: ${results.overall.toUpperCase()}`);
  
  if (results.overall === 'healthy') {
    console.log('🎉 All systems operational!');
  } else {
    console.log('⚠️  Some services need attention');
  }
  
  console.log('\n📞 Support Contacts:');
  console.log('- Frontend issues: Check GitHub Pages deployment');
  console.log('- Backend issues: Check Render dashboard');
  console.log('- Database issues: Check PostgreSQL connection');
  console.log('- ML/Chatbot issues: Check respective Render services');
}

runHealthCheck().catch(console.error);
