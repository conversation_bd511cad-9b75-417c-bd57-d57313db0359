# MedReserve AI - Recommended .gitignore
# Copy these patterns to your .gitignore file to prevent unnecessary files from being committed

# ===================================
# BUILD ARTIFACTS & DEPENDENCIES
# ===================================

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build outputs
frontend/build/
frontend/dist/
frontend/.vite/
frontend/.cache/

# Backend build outputs
backend/target/
backend/out/
backend/build/

# Maven
.mvn/
mvnw
mvnw.cmd

# ===================================
# LOGS & TEMPORARY FILES
# ===================================

# Application logs
logs/
*.log
*.log.*
*.gz

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# ===================================
# ENVIRONMENT & CONFIGURATION
# ===================================

# Environment variables (keep .env.example)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===================================
# TEMPORARY & TEST FILES
# ===================================

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Test files (keep actual test directories)
test-*.js
*-test-*.js
comprehensive-*.js

# Backup files
*.bak
*.backup
*.orig

# ===================================
# DEPLOYMENT & DOCKER
# ===================================

# Docker
.dockerignore

# Deployment artifacts
*.war
*.jar
*.ear
*.zip
*.tar.gz
*.rar

# ===================================
# DOCUMENTATION ARTIFACTS
# ===================================

# Generated documentation
docs/build/
site/

# Temporary documentation files
*_TEMP.md
*_BACKUP.md
TEMP_*.md
BACKUP_*.md

# Analysis and report files
*_ANALYSIS.md
*_REPORT.md
COMPREHENSIVE_*.md

# ===================================
# SECURITY & SENSITIVE DATA
# ===================================

# Certificates and keys
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Database files
*.db
*.sqlite
*.sqlite3

# ===================================
# PLATFORM SPECIFIC
# ===================================

# Windows
*.exe
*.msi
*.cab
*.deb
*.rpm

# macOS
*.dmg
*.pkg

# Linux
*.AppImage
*.snap

# ===================================
# DEVELOPMENT TOOLS
# ===================================

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# TypeScript cache
*.tsbuildinfo

# Webpack cache
.webpack/

# Parcel cache
.parcel-cache/

# ===================================
# CUSTOM PROJECT PATTERNS
# ===================================

# Custom start scripts (keep main ones)
start-local.*
start-*-temp.*
*-temp.bat
*-temp.sh
*-temp.ps1

# Custom test files
test-integration-*.js
test-*-temp.js

# Upload directories (keep structure, ignore content)
backend/uploads/*
!backend/uploads/.gitkeep
frontend/uploads/*
!frontend/uploads/.gitkeep

# ===================================
# PACKAGE MANAGER LOCKS (OPTIONAL)
# ===================================

# Uncomment if you want to ignore lock files
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ===================================
# COMMENTS FOR TEAM
# ===================================

# Add any project-specific patterns below:
# Example: custom-config-*.json
# Example: local-development-*
