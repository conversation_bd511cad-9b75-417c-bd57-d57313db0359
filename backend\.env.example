# Database Configuration (PostgreSQL for Production)
# ⚠️ IMPORTANT: URL must start with jdbc:postgresql:// (not just postgresql://)
DB_URL=*********************************************************
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# Local Development (MySQL Alternative)
# DB_URL=*****************************************************************************************************************
# DB_USERNAME=root
# DB_PASSWORD=password

# Server Configuration
SERVER_PORT=8080

# JWT Configuration (Required)
JWT_SECRET=mySecretKey123456789012345678901234567890
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# File Upload Configuration
FILE_UPLOAD_DIR=./uploads

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# ML Service Configuration
# For production: https://your-ml-service.onrender.com
# For development: http://localhost:8001
ML_SERVICE_URL=http://localhost:8001

# Chatbot Service Configuration
# For production: https://your-chatbot-service.onrender.com
# For development: http://localhost:5005
CHATBOT_SERVICE_URL=http://localhost:5005

# Logging Configuration
LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=WARN
LOG_FILE=logs/medreserve.log
SHOW_SQL=false
