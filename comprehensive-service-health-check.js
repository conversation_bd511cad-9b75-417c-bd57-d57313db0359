const axios = require('axios');

// Service URLs
const services = {
  backend: 'https://medreserve-ai-backend.onrender.com',
  ml: 'https://medreserve-ml.onrender.com',
  chatbot: 'https://medreserve-chatbot.onrender.com',
  frontend: 'https://rishith2903.github.io/MedReserve-AI/'
};

// Test endpoints
const endpoints = {
  backend: [
    '/actuator/health',
    '/api/auth/health',
    '/api/doctors',
    '/api/appointments/available-slots?doctorId=1&date=2024-01-15'
  ],
  ml: [
    '/health',
    '/predict-specialty',
    '/models/status'
  ],
  chatbot: [
    '/health',
    '/chat/health',
    '/webhook/dialogflow'
  ]
};

async function checkServiceHealth(serviceName, baseUrl) {
  console.log(`\n🔍 Checking ${serviceName.toUpperCase()} Service: ${baseUrl}`);
  console.log('=' .repeat(60));
  
  const results = {
    service: serviceName,
    baseUrl: baseUrl,
    status: 'unknown',
    responseTime: 0,
    endpoints: [],
    errors: []
  };

  try {
    const startTime = Date.now();
    
    // Basic connectivity test
    const response = await axios.get(baseUrl, {
      timeout: 10000,
      validateStatus: () => true // Accept any status code
    });
    
    results.responseTime = Date.now() - startTime;
    results.status = response.status < 500 ? 'healthy' : 'unhealthy';
    
    console.log(`✅ Base URL accessible: ${response.status} (${results.responseTime}ms)`);
    
    // Test specific endpoints
    if (endpoints[serviceName]) {
      for (const endpoint of endpoints[serviceName]) {
        try {
          const endpointStart = Date.now();
          const endpointResponse = await axios.get(`${baseUrl}${endpoint}`, {
            timeout: 8000,
            validateStatus: () => true
          });
          
          const endpointTime = Date.now() - endpointStart;
          const endpointResult = {
            path: endpoint,
            status: endpointResponse.status,
            responseTime: endpointTime,
            healthy: endpointResponse.status < 500
          };
          
          results.endpoints.push(endpointResult);
          
          const statusIcon = endpointResult.healthy ? '✅' : '❌';
          console.log(`${statusIcon} ${endpoint}: ${endpointResponse.status} (${endpointTime}ms)`);
          
        } catch (endpointError) {
          const endpointResult = {
            path: endpoint,
            status: 'error',
            responseTime: 0,
            healthy: false,
            error: endpointError.message
          };
          
          results.endpoints.push(endpointResult);
          console.log(`❌ ${endpoint}: ERROR - ${endpointError.message}`);
        }
      }
    }
    
  } catch (error) {
    results.status = 'error';
    results.errors.push(error.message);
    console.log(`❌ Service unreachable: ${error.message}`);
  }
  
  return results;
}

async function checkCrossServiceConnectivity() {
  console.log('\n🔗 Testing Cross-Service Connectivity');
  console.log('=' .repeat(60));
  
  try {
    // Test backend -> ML service
    console.log('Testing Backend -> ML Service...');
    const mlTestResponse = await axios.post(`${services.backend}/api/ai/predict-specialty`, {
      symptoms: ['fever', 'cough']
    }, {
      timeout: 15000,
      validateStatus: () => true
    });
    
    console.log(`Backend -> ML: ${mlTestResponse.status} (${mlTestResponse.status < 500 ? '✅' : '❌'})`);
    
    // Test backend -> Chatbot service
    console.log('Testing Backend -> Chatbot Service...');
    const chatTestResponse = await axios.post(`${services.backend}/api/chatbot/webhook`, {
      message: 'Hello',
      language: 'en'
    }, {
      timeout: 15000,
      validateStatus: () => true
    });
    
    console.log(`Backend -> Chatbot: ${chatTestResponse.status} (${chatTestResponse.status < 500 ? '✅' : '❌'})`);
    
  } catch (error) {
    console.log(`❌ Cross-service connectivity error: ${error.message}`);
  }
}

async function checkEnvironmentVariables() {
  console.log('\n🔧 Environment Variables Analysis');
  console.log('=' .repeat(60));
  
  try {
    // Check backend environment info
    const envResponse = await axios.get(`${services.backend}/actuator/env`, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (envResponse.status === 200) {
      console.log('✅ Backend environment endpoint accessible');
      // Note: Don't log sensitive data in production
    } else {
      console.log('⚠️ Backend environment endpoint not accessible (this is normal for security)');
    }
    
  } catch (error) {
    console.log('⚠️ Environment check not available (this is normal for security)');
  }
}

async function performComprehensiveHealthCheck() {
  console.log('🏥 MedReserve AI - Comprehensive Service Health Check');
  console.log('=' .repeat(80));
  console.log(`Started at: ${new Date().toISOString()}`);
  
  const results = [];
  
  // Check each service
  for (const [serviceName, url] of Object.entries(services)) {
    if (serviceName !== 'frontend') { // Skip frontend for API checks
      const result = await checkServiceHealth(serviceName, url);
      results.push(result);
    }
  }
  
  // Check cross-service connectivity
  await checkCrossServiceConnectivity();
  
  // Check environment variables
  await checkEnvironmentVariables();
  
  // Summary
  console.log('\n📊 HEALTH CHECK SUMMARY');
  console.log('=' .repeat(60));
  
  results.forEach(result => {
    const statusIcon = result.status === 'healthy' ? '✅' : 
                      result.status === 'error' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${result.service.toUpperCase()}: ${result.status} (${result.responseTime}ms)`);
    
    const healthyEndpoints = result.endpoints.filter(ep => ep.healthy).length;
    const totalEndpoints = result.endpoints.length;
    if (totalEndpoints > 0) {
      console.log(`   └─ Endpoints: ${healthyEndpoints}/${totalEndpoints} healthy`);
    }
  });
  
  // Performance analysis
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  console.log(`\n⏱️ Average Response Time: ${Math.round(avgResponseTime)}ms`);
  
  if (avgResponseTime > 2000) {
    console.log('⚠️ High response times detected - consider performance optimization');
  }
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  results.forEach(result => {
    if (result.responseTime > 2000) {
      console.log(`⚠️ ${result.service}: Response time is high (${result.responseTime}ms) - check for cold starts`);
    }
    if (result.errors.length > 0) {
      console.log(`❌ ${result.service}: Has errors - check logs and configuration`);
    }
  });
  
  console.log('\n✅ Health check completed!');
}

// Run the health check
performComprehensiveHealthCheck().catch(console.error);
