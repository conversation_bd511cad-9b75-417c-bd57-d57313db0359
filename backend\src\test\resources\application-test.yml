spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  h2:
    console:
      enabled: true

jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 86400000
  refresh-expiration: 604800000

logging:
  level:
    com.medreserve: DEBUG
    org.springframework.security: DEBUG
