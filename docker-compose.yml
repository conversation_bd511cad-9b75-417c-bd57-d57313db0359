version: '3.8'

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:8080
    depends_on:
      - backend
    networks:
      - medreserve-network

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_URL=************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
      - JWT_EXPIRATION=86400000
      - CORS_ALLOWED_ORIGINS=http://localhost:3000
      - ML_SERVICE_URL=http://ml-service:5000
      - CHATBOT_SERVICE_URL=http://chatbot-service:8081
    depends_on:
      - db
      - ml-service
      - chatbot-service
    networks:
      - medreserve-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ML Service
  ml-service:
    build:
      context: ./backend/ml
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - PORT=5000
    networks:
      - medreserve-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Chatbot Service
  chatbot-service:
    build:
      context: ./backend/chatbot
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - PORT=8081
    networks:
      - medreserve-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB=medreserve
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/data.sql:/docker-entrypoint-initdb.d/data.sql
    ports:
      - "5432:5432"
    networks:
      - medreserve-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (for caching - optional)
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medreserve-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  medreserve-network:
    driver: bridge
