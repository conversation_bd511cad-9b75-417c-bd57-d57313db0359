const fs = require('fs');
const path = require('path');

console.log('📁 MedReserve Full-Stack Folder Structure Analysis');
console.log('=' .repeat(70));

// Analysis results
const analysisResults = {
  structure: { status: 'unknown', details: null },
  imports: { status: 'unknown', details: null },
  routes: { status: 'unknown', details: null },
  configs: { status: 'unknown', details: null },
  missingFiles: { status: 'unknown', details: null }
};

async function analyzeFolderStructure() {
  
  // 1. Analyze Project Structure
  console.log('\n1️⃣ Analyzing Project Structure...');
  try {
    const projectStructure = await analyzeProjectStructure();
    analysisResults.structure.status = 'success';
    analysisResults.structure.details = projectStructure;
    console.log('✅ Project structure analysis complete');
  } catch (error) {
    analysisResults.structure.status = 'error';
    analysisResults.structure.details = { error: error.message };
    console.log(`❌ Project structure analysis failed: ${error.message}`);
  }

  // 2. Check Import/Export Consistency
  console.log('\n2️⃣ Checking Import/Export Consistency...');
  try {
    const importIssues = await checkImportConsistency();
    analysisResults.imports.status = importIssues.length === 0 ? 'success' : 'issues_found';
    analysisResults.imports.details = importIssues;
    
    if (importIssues.length === 0) {
      console.log('✅ All imports/exports are consistent');
    } else {
      console.log(`⚠️ Found ${importIssues.length} import/export issues`);
      importIssues.slice(0, 5).forEach(issue => {
        console.log(`   - ${issue.file}: ${issue.issue}`);
      });
      if (importIssues.length > 5) {
        console.log(`   ... and ${importIssues.length - 5} more issues`);
      }
    }
  } catch (error) {
    analysisResults.imports.status = 'error';
    analysisResults.imports.details = { error: error.message };
    console.log(`❌ Import consistency check failed: ${error.message}`);
  }

  // 3. Validate Route Configurations
  console.log('\n3️⃣ Validating Route Configurations...');
  try {
    const routeIssues = await validateRoutes();
    analysisResults.routes.status = routeIssues.length === 0 ? 'success' : 'issues_found';
    analysisResults.routes.details = routeIssues;
    
    if (routeIssues.length === 0) {
      console.log('✅ All routes are properly configured');
    } else {
      console.log(`⚠️ Found ${routeIssues.length} route configuration issues`);
      routeIssues.slice(0, 5).forEach(issue => {
        console.log(`   - ${issue.file}: ${issue.issue}`);
      });
      if (routeIssues.length > 5) {
        console.log(`   ... and ${routeIssues.length - 5} more issues`);
      }
    }
  } catch (error) {
    analysisResults.routes.status = 'error';
    analysisResults.routes.details = { error: error.message };
    console.log(`❌ Route validation failed: ${error.message}`);
  }

  // 4. Check Configuration Files
  console.log('\n4️⃣ Checking Configuration Files...');
  try {
    const configIssues = await checkConfigurationFiles();
    analysisResults.configs.status = configIssues.length === 0 ? 'success' : 'issues_found';
    analysisResults.configs.details = configIssues;
    
    if (configIssues.length === 0) {
      console.log('✅ All configuration files are present and valid');
    } else {
      console.log(`⚠️ Found ${configIssues.length} configuration issues`);
      configIssues.forEach(issue => {
        console.log(`   - ${issue.file}: ${issue.issue}`);
      });
    }
  } catch (error) {
    analysisResults.configs.status = 'error';
    analysisResults.configs.details = { error: error.message };
    console.log(`❌ Configuration check failed: ${error.message}`);
  }

  // 5. Check for Missing Critical Files
  console.log('\n5️⃣ Checking for Missing Critical Files...');
  try {
    const missingFiles = await checkMissingFiles();
    analysisResults.missingFiles.status = missingFiles.length === 0 ? 'success' : 'issues_found';
    analysisResults.missingFiles.details = missingFiles;
    
    if (missingFiles.length === 0) {
      console.log('✅ All critical files are present');
    } else {
      console.log(`⚠️ Found ${missingFiles.length} missing critical files`);
      missingFiles.forEach(file => {
        console.log(`   - Missing: ${file.path} (${file.reason})`);
      });
    }
  } catch (error) {
    analysisResults.missingFiles.status = 'error';
    analysisResults.missingFiles.details = { error: error.message };
    console.log(`❌ Missing files check failed: ${error.message}`);
  }

  // Print Summary
  printAnalysisSummary();
}

async function analyzeProjectStructure() {
  const structure = {
    frontend: await getDirectoryStructure('frontend'),
    backend: await getDirectoryStructure('backend'),
    root: await getRootFiles()
  };
  
  return structure;
}

async function getDirectoryStructure(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return { error: `Directory ${dirPath} does not exist` };
  }
  
  const structure = {};
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      // Only go 2 levels deep to avoid overwhelming output
      if (dirPath.split(path.sep).length < 3) {
        structure[item] = await getDirectoryStructure(itemPath);
      } else {
        structure[item] = '[directory]';
      }
    } else {
      structure[item] = '[file]';
    }
  }
  
  return structure;
}

async function getRootFiles() {
  const rootFiles = fs.readdirSync('.')
    .filter(item => fs.statSync(item).isFile())
    .map(file => `[file] ${file}`);
  
  return rootFiles;
}

async function checkImportConsistency() {
  const issues = [];
  
  // Check frontend imports
  const frontendIssues = await checkFrontendImports();
  issues.push(...frontendIssues);
  
  // Check backend imports (Java)
  const backendIssues = await checkBackendImports();
  issues.push(...backendIssues);
  
  return issues;
}

async function checkFrontendImports() {
  const issues = [];
  const frontendSrcPath = 'frontend/src';
  
  if (!fs.existsSync(frontendSrcPath)) {
    return [{ file: 'frontend/src', issue: 'Frontend src directory not found' }];
  }
  
  // Get all JS/JSX files
  const jsFiles = await getAllJSFiles(frontendSrcPath);
  
  for (const file of jsFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const importIssues = analyzeJSImports(file, content);
      issues.push(...importIssues);
    } catch (error) {
      issues.push({ file, issue: `Failed to read file: ${error.message}` });
    }
  }
  
  return issues;
}

async function getAllJSFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(itemPath);
      } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx'))) {
        files.push(itemPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function analyzeJSImports(filePath, content) {
  const issues = [];
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Check for relative imports
    if (line.startsWith('import') && line.includes('./') || line.includes('../')) {
      const match = line.match(/from\s+['"]([^'"]+)['"]/);
      if (match) {
        const importPath = match[1];
        const resolvedPath = path.resolve(path.dirname(filePath), importPath);
        
        // Check if the imported file exists (with common extensions)
        const possibleExtensions = ['', '.js', '.jsx', '.ts', '.tsx'];
        let exists = false;
        
        for (const ext of possibleExtensions) {
          if (fs.existsSync(resolvedPath + ext)) {
            exists = true;
            break;
          }
        }
        
        if (!exists) {
          issues.push({
            file: filePath,
            issue: `Import not found: ${importPath} (line ${i + 1})`
          });
        }
      }
    }
  }
  
  return issues;
}

async function checkBackendImports() {
  const issues = [];
  // For Java, we'll check for basic package structure consistency
  const backendSrcPath = 'backend/src/main/java';
  
  if (!fs.existsSync(backendSrcPath)) {
    return [{ file: 'backend/src/main/java', issue: 'Backend Java source directory not found' }];
  }
  
  // Basic check for package structure
  const javaFiles = await getAllJavaFiles(backendSrcPath);
  
  for (const file of javaFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const packageIssues = analyzeJavaPackages(file, content);
      issues.push(...packageIssues);
    } catch (error) {
      issues.push({ file, issue: `Failed to read file: ${error.message}` });
    }
  }
  
  return issues;
}

async function getAllJavaFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        traverse(itemPath);
      } else if (stat.isFile() && item.endsWith('.java')) {
        files.push(itemPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function analyzeJavaPackages(filePath, content) {
  const issues = [];
  const lines = content.split('\n');
  
  // Check if package declaration matches file path
  const packageLine = lines.find(line => line.trim().startsWith('package '));
  if (packageLine) {
    const packageName = packageLine.match(/package\s+([^;]+);/);
    if (packageName) {
      const expectedPath = packageName[1].replace(/\./g, path.sep);
      const actualPath = path.dirname(filePath).replace(/\\/g, '/');
      
      if (!actualPath.includes(expectedPath.replace(/\\/g, '/'))) {
        issues.push({
          file: filePath,
          issue: `Package declaration doesn't match file path: ${packageName[1]}`
        });
      }
    }
  }
  
  return issues;
}

async function validateRoutes() {
  const issues = [];
  
  // Check React routes
  const reactRouteIssues = await checkReactRoutes();
  issues.push(...reactRouteIssues);
  
  // Check Spring Boot routes
  const springRouteIssues = await checkSpringRoutes();
  issues.push(...springRouteIssues);
  
  return issues;
}

async function checkReactRoutes() {
  const issues = [];
  const appJsxPath = 'frontend/src/App.jsx';
  
  if (!fs.existsSync(appJsxPath)) {
    return [{ file: appJsxPath, issue: 'Main App.jsx file not found' }];
  }
  
  try {
    const content = fs.readFileSync(appJsxPath, 'utf8');
    
    // Check for route components that might not exist
    const routeMatches = content.match(/<Route[^>]+element=\{<([^>]+)>/g);
    if (routeMatches) {
      for (const match of routeMatches) {
        const componentMatch = match.match(/element=\{<([^>\s]+)/);
        if (componentMatch) {
          const componentName = componentMatch[1];
          
          // Check if component is imported
          if (!content.includes(`import ${componentName}`) && !content.includes(`import { ${componentName}`)) {
            issues.push({
              file: appJsxPath,
              issue: `Route component ${componentName} is not imported`
            });
          }
        }
      }
    }
  } catch (error) {
    issues.push({ file: appJsxPath, issue: `Failed to analyze routes: ${error.message}` });
  }
  
  return issues;
}

async function checkSpringRoutes() {
  const issues = [];
  // This would require more complex analysis of Spring Boot controllers
  // For now, we'll do a basic check
  
  const controllersPath = 'backend/src/main/java/com/medreserve/controller';
  if (!fs.existsSync(controllersPath)) {
    return [{ file: controllersPath, issue: 'Controllers directory not found' }];
  }
  
  return issues;
}

async function checkConfigurationFiles() {
  const issues = [];
  
  const criticalConfigs = [
    { path: 'frontend/package.json', description: 'Frontend package configuration' },
    { path: 'frontend/vite.config.js', description: 'Vite build configuration' },
    { path: 'frontend/.env.local', description: 'Frontend local environment variables' },
    { path: 'frontend/.env.production', description: 'Frontend production environment variables' },
    { path: 'backend/pom.xml', description: 'Backend Maven configuration' },
    { path: 'backend/src/main/resources/application.yml', description: 'Backend main configuration' },
    { path: 'backend/src/main/resources/application-local.yml', description: 'Backend local configuration' },
    { path: 'backend/src/main/resources/application-production.yml', description: 'Backend production configuration' }
  ];
  
  for (const config of criticalConfigs) {
    if (!fs.existsSync(config.path)) {
      issues.push({
        file: config.path,
        issue: `Missing ${config.description}`
      });
    } else {
      // Basic validation for specific config files
      try {
        if (config.path.endsWith('.json')) {
          const content = fs.readFileSync(config.path, 'utf8');
          JSON.parse(content); // Validate JSON syntax
        }
      } catch (error) {
        issues.push({
          file: config.path,
          issue: `Invalid JSON syntax: ${error.message}`
        });
      }
    }
  }
  
  return issues;
}

async function checkMissingFiles() {
  const missingFiles = [];
  
  const criticalFiles = [
    { path: 'README.md', reason: 'Project documentation' },
    { path: 'frontend/src/index.js', reason: 'Frontend entry point' },
    { path: 'frontend/src/App.jsx', reason: 'Main React component' },
    { path: 'backend/src/main/java/com/medreserve/MedReserveApplication.java', reason: 'Spring Boot main class' },
    { path: '.gitignore', reason: 'Git ignore configuration' }
  ];
  
  for (const file of criticalFiles) {
    if (!fs.existsSync(file.path)) {
      missingFiles.push(file);
    }
  }
  
  return missingFiles;
}

function printAnalysisSummary() {
  console.log('\n📊 FOLDER STRUCTURE ANALYSIS SUMMARY');
  console.log('=' .repeat(70));
  
  const results = Object.entries(analysisResults);
  let successCount = 0;
  let totalCount = 0;
  
  for (const [category, result] of results) {
    const icon = result.status === 'success' ? '✅' : 
                 result.status === 'issues_found' ? '⚠️' : '❌';
    const categoryName = category.toUpperCase().replace(/([A-Z])/g, ' $1').trim();
    
    console.log(`${icon} ${categoryName}: ${result.status}`);
    
    if (result.status === 'success') successCount++;
    if (result.status !== 'skipped') totalCount++;
  }
  
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (analysisResults.structure.status === 'error') {
    console.log('⚠️ Project structure analysis failed - check file permissions');
  }
  
  if (analysisResults.imports.status === 'issues_found') {
    console.log('⚠️ Import/export issues detected - fix broken imports');
  }
  
  if (analysisResults.routes.status === 'issues_found') {
    console.log('⚠️ Route configuration issues - check component imports');
  }
  
  if (analysisResults.configs.status === 'issues_found') {
    console.log('⚠️ Configuration issues - verify all config files');
  }
  
  if (analysisResults.missingFiles.status === 'issues_found') {
    console.log('⚠️ Missing critical files - restore missing files');
  }
  
  console.log(`\n🎯 Overall Analysis Score: ${successCount}/${totalCount} categories passed`);
  
  if (successCount === totalCount) {
    console.log('🎉 Folder structure analysis completed successfully!');
    console.log('✅ Project structure is well-organized and consistent');
  } else {
    console.log('⚠️ Some structural issues detected. Review the issues above.');
  }
}

// Run the folder structure analysis
analyzeFolderStructure().catch(console.error);
