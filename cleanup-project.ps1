# MedReserve Project Cleanup Script
# This script removes unnecessary files and build artifacts to clean up the project

Write-Host "🧹 MedReserve Project Cleanup Script" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Function to safely remove files/directories
function Remove-SafelyWithConfirmation {
    param(
        [string]$Path,
        [string]$Description,
        [switch]$Force = $false
    )
    
    if (Test-Path $Path) {
        if ($Force) {
            Write-Host "🗑️  Removing: $Description" -ForegroundColor Yellow
            Remove-Item $Path -Recurse -Force -ErrorAction SilentlyContinue
            if (-not (Test-Path $Path)) {
                Write-Host "   ✅ Removed successfully" -ForegroundColor Green
            } else {
                Write-Host "   ❌ Failed to remove" -ForegroundColor Red
            }
        } else {
            $response = Read-Host "Remove $Description? (y/N)"
            if ($response -eq 'y' -or $response -eq 'Y') {
                Write-Host "🗑️  Removing: $Description" -ForegroundColor Yellow
                Remove-Item $Path -Recurse -Force -ErrorAction SilentlyContinue
                if (-not (Test-Path $Path)) {
                    Write-Host "   ✅ Removed successfully" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ Failed to remove" -ForegroundColor Red
                }
            } else {
                Write-Host "   ⏭️  Skipped" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "   ℹ️  Not found: $Path" -ForegroundColor Gray
    }
}

# Get current directory
$projectRoot = Get-Location

Write-Host "📁 Project Root: $projectRoot" -ForegroundColor Blue
Write-Host ""

# Ask for cleanup mode
Write-Host "Choose cleanup mode:" -ForegroundColor Cyan
Write-Host "1. Interactive (ask for each item)" -ForegroundColor White
Write-Host "2. Safe Auto (remove build artifacts only)" -ForegroundColor White
Write-Host "3. Full Auto (remove all unnecessary files)" -ForegroundColor White
$mode = Read-Host "Enter choice (1-3)"

$autoMode = $mode -eq "2" -or $mode -eq "3"
$fullAuto = $mode -eq "3"

Write-Host ""
Write-Host "🚀 Starting cleanup..." -ForegroundColor Green
Write-Host ""

# 1. ROOT LEVEL CLEANUP
Write-Host "📂 ROOT LEVEL CLEANUP" -ForegroundColor Magenta
Write-Host "=====================" -ForegroundColor Magenta

Remove-SafelyWithConfirmation -Path ".\node_modules" -Description "Root node_modules directory" -Force:$autoMode
Remove-SafelyWithConfirmation -Path ".\package.json" -Description "Root package.json" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\package-lock.json" -Description "Root package-lock.json" -Force:$fullAuto

# 2. TEMPORARY TEST FILES
Write-Host ""
Write-Host "🧪 TEMPORARY TEST FILES" -ForegroundColor Magenta
Write-Host "=======================" -ForegroundColor Magenta

Remove-SafelyWithConfirmation -Path ".\comprehensive-api-test.js" -Description "Comprehensive API test file" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\test-disease-prediction.js" -Description "Disease prediction test file" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\test-integration.js" -Description "Integration test file" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\test-multilingual-chatbot.js" -Description "Multilingual chatbot test file" -Force:$fullAuto

# 3. DUPLICATE DOCUMENTATION
Write-Host ""
Write-Host "📄 DUPLICATE DOCUMENTATION" -ForegroundColor Magenta
Write-Host "===========================" -ForegroundColor Magenta

Remove-SafelyWithConfirmation -Path ".\DEPLOYMENT_CHECKLIST.md" -Description "Deployment checklist" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\LOCAL_DEVELOPMENT.md" -Description "Local development guide" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\MULTILINGUAL_CHATBOT_INTEGRATION_COMPLETE.md" -Description "Chatbot integration doc" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\QUICK_START.md" -Description "Quick start guide" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\START_HERE.md" -Description "Start here guide" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\COMPREHENSIVE_ANALYSIS_REPORT.md" -Description "Analysis report" -Force:$fullAuto

# 4. BUILD ARTIFACTS
Write-Host ""
Write-Host "🏗️  BUILD ARTIFACTS" -ForegroundColor Magenta
Write-Host "===================" -ForegroundColor Magenta

Remove-SafelyWithConfirmation -Path ".\backend\target" -Description "Maven target directory" -Force:$autoMode
Remove-SafelyWithConfirmation -Path ".\backend\logs" -Description "Backend log files" -Force:$autoMode
Remove-SafelyWithConfirmation -Path ".\frontend\build" -Description "Frontend build directory" -Force:$autoMode
Remove-SafelyWithConfirmation -Path ".\frontend\dist" -Description "Frontend dist directory" -Force:$autoMode
Remove-SafelyWithConfirmation -Path ".\frontend\node_modules" -Description "Frontend node_modules" -Force:$autoMode

# 5. TEMPORARY SCRIPTS
Write-Host ""
Write-Host "🔧 TEMPORARY SCRIPTS" -ForegroundColor Magenta
Write-Host "====================" -ForegroundColor Magenta

Remove-SafelyWithConfirmation -Path ".\start-local.ps1" -Description "Local start script" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\start-medreserve-with-chatbot.bat" -Description "Start with chatbot script" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\start-medreserve.bat" -Description "Start medreserve script" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\start-medreserve.sh" -Description "Start medreserve shell script" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\backend\test-deployment.sh" -Description "Backend test deployment script" -Force:$fullAuto

# 6. DUPLICATE DEPLOYMENT FILES
Write-Host ""
Write-Host "🐳 DEPLOYMENT FILES REVIEW" -ForegroundColor Magenta
Write-Host "===========================" -ForegroundColor Magenta

Remove-SafelyWithConfirmation -Path ".\docker-compose.local.yml" -Description "Local docker compose file" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\frontend\Dockerfile.dev" -Description "Frontend dev Dockerfile" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\backend\README_DEPLOYMENT.md" -Description "Backend deployment README" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\frontend\DEPLOY.md" -Description "Frontend deploy guide" -Force:$fullAuto
Remove-SafelyWithConfirmation -Path ".\frontend\PROJECT_STRUCTURE.md" -Description "Frontend project structure" -Force:$fullAuto

# 7. PLATFORM-SPECIFIC FILES (Optional)
Write-Host ""
Write-Host "🌐 PLATFORM-SPECIFIC FILES" -ForegroundColor Magenta
Write-Host "===========================" -ForegroundColor Magenta
Write-Host "⚠️  Only remove if you're not using these platforms:" -ForegroundColor Yellow

if (-not $autoMode) {
    Remove-SafelyWithConfirmation -Path ".\backend\nixpacks.toml" -Description "Nixpacks config (remove if not using Nixpacks)"
    Remove-SafelyWithConfirmation -Path ".\backend\railway.json" -Description "Railway config (remove if not using Railway)"
    Remove-SafelyWithConfirmation -Path ".\backend\Procfile" -Description "Heroku Procfile (remove if not using Heroku)"
}

Write-Host ""
Write-Host "✨ CLEANUP SUMMARY" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# Calculate remaining important files
$importantFiles = @(
    "README.md",
    "backend\pom.xml",
    "backend\src",
    "backend\render.yaml",
    "frontend\package.json",
    "frontend\src",
    "frontend\vite.config.js"
)

Write-Host "✅ Important files preserved:" -ForegroundColor Green
foreach ($file in $importantFiles) {
    if (Test-Path $file) {
        Write-Host "   ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  $file (not found)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Cleanup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Run 'cd frontend && npm install' to reinstall frontend dependencies" -ForegroundColor White
Write-Host "2. Run 'cd backend && mvn clean compile' to rebuild backend" -ForegroundColor White
Write-Host "3. Test the application to ensure everything works" -ForegroundColor White
Write-Host ""
Write-Host "💡 Tip: Add these patterns to .gitignore to prevent future clutter:" -ForegroundColor Yellow
Write-Host "   - node_modules/" -ForegroundColor Gray
Write-Host "   - target/" -ForegroundColor Gray
Write-Host "   - dist/" -ForegroundColor Gray
Write-Host "   - build/" -ForegroundColor Gray
Write-Host "   - logs/" -ForegroundColor Gray
Write-Host ""

# Offer to create .gitignore
if (-not $autoMode) {
    $createGitignore = Read-Host "Would you like to update .gitignore with recommended patterns? (y/N)"
    if ($createGitignore -eq 'y' -or $createGitignore -eq 'Y') {
        Write-Host "📝 Updating .gitignore..." -ForegroundColor Blue
        # This will be handled in a separate script call
        Write-Host "   ℹ️  Please manually add the recommended patterns to .gitignore" -ForegroundColor Gray
    }
}

Write-Host "🏁 Script completed successfully!" -ForegroundColor Green
